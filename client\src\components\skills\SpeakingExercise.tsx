import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useSpeak } from "@/hooks/use-speak";

interface SpeakingExerciseProps {
  exerciseId: number;
}

export function SpeakingExercise({ exerciseId }: SpeakingExerciseProps) {
  const { data: exercise, isLoading } = useQuery<Exercise>({
    queryKey: [`/api/exercises/${exerciseId}`],
  });

  const { speak, startListening, stopListening, isSpeaking, isListening, transcript } = useSpeak();
  const [messages, setMessages] = useState<Array<{ text: string; isUser: boolean }>>([]);
  const [userInput, setUserInput] = useState("");
  const [currentPrompt, setCurrentPrompt] = useState<number>(0);
  const [feedback, setFeedback] = useState<string | null>(null);

  useEffect(() => {
    if (exercise && messages.length === 0) {
      const { prompts } = exercise.content as any;
      if (prompts && prompts.length > 0) {
        const initialMessage = { text: prompts[0], isUser: false };
        setMessages([initialMessage]);
        speak(prompts[0]);
      }
    }
  }, [exercise, messages.length, speak]);

  useEffect(() => {
    if (transcript) {
      setUserInput(transcript);
    }
  }, [transcript]);

  if (isLoading) {
    return (
      <div className="mt-8 border border-neutral-medium rounded-lg p-6">
        <Skeleton className="h-7 w-72 mb-4" />
        
        <div className="border border-neutral-medium rounded-lg overflow-hidden">
          <div className="bg-neutral-light p-4 border-b border-neutral-medium">
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="ml-3">
                <Skeleton className="h-5 w-32 mb-1" />
                <Skeleton className="h-4 w-48" />
              </div>
            </div>
          </div>
          
          <div className="h-96 p-4 bg-neutral-light bg-opacity-50">
            {[...Array(3)].map((_, i) => (
              <div key={i} className={`flex mb-4 ${i % 2 === 0 ? "" : "justify-end"}`}>
                <Skeleton className={`h-10 w-10 rounded-full ${i % 2 === 0 ? "mr-3" : "ml-3 order-2"}`} />
                <Skeleton className={`h-20 w-64 rounded-lg ${i % 2 === 0 ? "" : "order-1"}`} />
              </div>
            ))}
          </div>
          
          <div className="border-t border-neutral-medium p-4">
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 rounded-full mr-3" />
              <Skeleton className="h-10 flex-grow rounded-lg" />
              <Skeleton className="h-10 w-10 rounded-full ml-3" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!exercise) {
    return <div>Exercise not found</div>;
  }

  const { avatar, prompts, responses, feedbacks } = exercise.content as any;

  const handleSendMessage = () => {
    if (userInput.trim() === "") return;
    
    // Add user message
    const newUserMessage = { text: userInput, isUser: true };
    setMessages(prevMessages => [...prevMessages, newUserMessage]);
    
    // Generate bot response
    setTimeout(() => {
      // In a real app, this would use NLP to analyze the input and generate appropriate responses
      const userInputLower = userInput.toLowerCase();
      
      // Check if we should provide feedback on the previous exchange
      setFeedback(feedbacks[currentPrompt]);
      
      // Move to next prompt if available
      if (currentPrompt < prompts.length - 1) {
        const nextPrompt = prompts[currentPrompt + 1];
        const botResponse = { text: nextPrompt, isUser: false };
        setMessages(prevMessages => [...prevMessages, botResponse]);
        setCurrentPrompt(currentPrompt + 1);
        speak(nextPrompt);
      } else {
        // End of exercise
        const finalResponse = { 
          text: "Vielen Dank für das Gespräch! Du hast die Übung abgeschlossen.", 
          isUser: false 
        };
        setMessages(prevMessages => [...prevMessages, finalResponse]);
        speak(finalResponse.text);
      }
    }, 1000);
    
    setUserInput("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleMicClick = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  return (
    <div className="mt-8 border border-neutral-medium rounded-lg p-6">
      <h3 className="font-bold text-neutral-darker mb-4">Speaking Exercise: {exercise.title}</h3>
      
      <div className="border border-neutral-medium rounded-lg overflow-hidden">
        {/* Avatar Header */}
        <div className="bg-neutral-light p-4 border-b border-neutral-medium flex items-center justify-between">
          <div className="flex items-center">
            <img 
              src={avatar?.imageUrl || "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150"} 
              alt="German tutor avatar" 
              className="w-10 h-10 rounded-full object-cover"
            />
            <div className="ml-3">
              <h3 className="font-medium text-neutral-darker">{avatar?.name || "Frau Schmidt"}</h3>
              <p className="text-xs text-neutral-dark">German Conversation Partner</p>
            </div>
          </div>
          <div>
            <select className="bg-white border border-neutral-medium rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
              <option>Topic: {exercise.title}</option>
              <option disabled>Other topics coming soon</option>
            </select>
          </div>
        </div>
        
        {/* Chat Area */}
        <div className="h-96 overflow-y-auto p-4 bg-neutral-light bg-opacity-50">
          {messages.map((message, index) => (
            <div key={index} className={`flex mb-4 ${message.isUser ? "justify-end" : ""}`}>
              {!message.isUser && (
                <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0 mr-3">
                  <img 
                    src={avatar?.imageUrl || "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150"} 
                    alt="Avatar" 
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className={`${
                message.isUser 
                  ? "bg-primary bg-opacity-10 mr-3" 
                  : "bg-white ml-3"
                } rounded-lg py-2 px-4 max-w-[80%]`}
              >
                <p className="text-neutral-darker">{message.text}</p>
              </div>
              {message.isUser && (
                <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0 bg-neutral-light flex items-center justify-center">
                  <span className="material-icons text-neutral-dark">person</span>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* Feedback Area */}
        {feedback && (
          <div className="border-t border-neutral-medium p-4 bg-success bg-opacity-10">
            <div className="flex items-start">
              <span className="material-icons text-success mr-2 mt-0.5">check_circle</span>
              <div>
                <h4 className="font-medium text-neutral-darker">Feedback</h4>
                <p className="text-sm text-neutral-dark">{feedback}</p>
              </div>
            </div>
          </div>
        )}
        
        {/* Input Area */}
        <div className="border-t border-neutral-medium p-4 flex items-center">
          <button 
            className={`rounded-full w-10 h-10 flex items-center justify-center mr-3 ${
              isListening ? "bg-primary text-white" : "bg-neutral-light text-neutral-darker"
            }`}
            onClick={handleMicClick}
          >
            <span className="material-icons">mic</span>
          </button>
          <Input
            placeholder="Type in German..."
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyDown={handleKeyPress}
            className="flex-grow"
          />
          <button 
            className="bg-primary rounded-full w-10 h-10 flex items-center justify-center ml-3 text-white"
            onClick={handleSendMessage}
            disabled={userInput.trim() === ""}
          >
            <span className="material-icons">send</span>
          </button>
        </div>
      </div>
    </div>
  );
}
