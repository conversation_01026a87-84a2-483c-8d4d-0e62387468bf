import { useQuery } from "@tanstack/react-query";
import { Skill } from "@shared/schema";
import { Link } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";

interface SuggestedActivitiesProps {
  userId: number;
}

export function SuggestedActivities({ userId }: SuggestedActivitiesProps) {
  const { data: skills, isLoading } = useQuery<Skill[]>({
    queryKey: [`/api/skills/${userId}`],
  });

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <Skeleton className="h-7 w-48 mb-4" />
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-neutral-light rounded-lg p-4">
              <div className="flex items-start">
                <Skeleton className="h-6 w-6 mr-3 mt-1" />
                <div className="w-full">
                  <Skeleton className="h-5 w-3/4 mb-1" />
                  <Skeleton className="h-4 w-2/3 mb-2" />
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Find weakest skill
  const getWeakestSkill = () => {
    if (!skills || skills.length === 0) return null;
    
    return skills.reduce((min, skill) => 
      skill.proficiency < min.proficiency ? skill : min, skills[0]
    );
  };

  const weakestSkill = getWeakestSkill();

  // Generate suggestions based on user skills and activity
  const suggestions = [
    {
      id: 1,
      icon: "record_voice_over",
      iconColor: "text-secondary",
      title: `Speaking Practice: Daily Routines`,
      description: "Improve your weakest skill area",
      action: "Start Now",
      link: "/speaking"
    },
    {
      id: 2,
      icon: "assignment",
      iconColor: "text-warning",
      title: "Complete A2 Writing Mock Test",
      description: "Test your progress with formal writing",
      action: "Start Test",
      link: "/mock-tests"
    },
    {
      id: 3,
      icon: "menu_book",
      iconColor: "text-success",
      title: "Review: Dative Prepositions",
      description: "Based on your previous errors",
      action: "Review",
      link: "/study-materials"
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-neutral-darker mb-4">Suggested Next Steps</h2>
      <ul className="space-y-4">
        {suggestions.map((suggestion) => (
          <li key={suggestion.id} className="bg-neutral-light rounded-lg p-4 hover:bg-blue-50 transition cursor-pointer">
            <div className="flex items-start">
              <span className={`material-icons ${suggestion.iconColor} mr-3 mt-1`}>
                {suggestion.icon}
              </span>
              <div>
                <h4 className="font-medium text-neutral-darker">{suggestion.title}</h4>
                <p className="text-sm text-neutral-dark">{suggestion.description}</p>
                <div className="flex mt-2">
                  <Link href={suggestion.link}>
                    <button className="bg-primary text-white text-sm rounded px-3 py-1 hover:bg-opacity-90 transition">
                      {suggestion.action}
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
