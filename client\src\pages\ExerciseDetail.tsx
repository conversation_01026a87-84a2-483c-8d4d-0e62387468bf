import { RxCross2 } from "react-icons/rx";

import { useRoute } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { ReadingExercise } from "@/components/skills/ReadingExercise";
import { WritingExercise } from "@/components/skills/WritingExercise";
import { ListeningExercise } from "@/components/skills/ListeningExercise";
import { SpeakingExercise } from "@/components/skills/SpeakingExercise";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useState } from "react";
import { FaMicrophone, FaMicrophoneSlash } from "react-icons/fa";
import { axiosInstance } from "@/lib/queryClient";
import AIAssistantDialog from "@/components/layout/ai-assiatant";

export default function ExerciseDetail() {
  const [open, setOpen] = useState(false);
  const [, params] = useRoute("/:skill/:id");
  const skill = params?.skill;
  const id = params?.id;
  const [isRecording, setIsRecording] = useState(false)
  const { data: exercise, isLoading } = useQuery<Exercise>({
    queryKey: [`/api/question/1/?skill=${skill}&uid=test-user-123`],
    queryFn: async () => {
      const res = await axiosInstance.get(`/api/question/1?skill=${skill}&uid=test-user-123`);
      return res.data;
    },
  });

  if (isLoading) {
    return (
      <div className="mt-8">
        <Skeleton className="h-8 w-64 mb-4" />
        <Skeleton className="h-40 w-full" />
      </div>
    );
  }



  const renderExercise = () => {
    switch (skill) {
      case "reading":
        return <ReadingExercise exerciseId={Number(id)} />;
      case "writing":
        return <WritingExercise exerciseId={Number(id)} />;
      case "listening":
        return <ListeningExercise exercise={exercise} isLoading={isLoading} />;
      case "speaking":
        return <SpeakingExercise exerciseId={Number(id)} />;
      default:
        return <div>Invalid exercise type</div>;
    }
  };



  return (
    <div className="max-w-4xl mx-auto rounded-xl ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{exercise?.title}</h1>
      </div>
      {renderExercise()}
      <div className="bg-white">
        <AIAssistantDialog
          open={open}
          setOpen={setOpen}
          isRecording={isRecording}
          setIsRecording={setIsRecording}
        />
      </div>
    </div>
  );
}