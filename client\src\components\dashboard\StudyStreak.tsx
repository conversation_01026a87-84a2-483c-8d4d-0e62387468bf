import { useQuery } from "@tanstack/react-query";
import { Streak } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";

interface StudyStreakProps {
  userId: number;
}

export function StudyStreak({ userId }: StudyStreakProps) {
  const { data: streaks, isLoading } = useQuery<Streak[]>({
    queryKey: [`/api/streaks/${userId}`],
  });

  if (isLoading) {
    return (
      <div className="bg-neutral-light rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
        <div className="flex justify-between">
          {[...Array(7)].map((_, i) => (
            <div key={i} className="flex flex-col items-center">
              <Skeleton className="w-8 h-8 rounded-full mb-1" />
              <Skeleton className="w-6 h-4" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Calculate streak length (consecutive days with activity)
  const calculateStreakLength = () => {
    if (!streaks || streaks.length === 0) return 0;
    
    // Sort streaks by date (newest first)
    const sortedStreaks = [...streaks].sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
    
    let streakCount = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < sortedStreaks.length; i++) {
      const streakDate = new Date(sortedStreaks[i].date);
      streakDate.setHours(0, 0, 0, 0);
      
      const expectedDate = new Date(today);
      expectedDate.setDate(today.getDate() - i);
      
      if (streakDate.getTime() === expectedDate.getTime() && sortedStreaks[i].duration > 0) {
        streakCount++;
      } else {
        break;
      }
    }
    
    return streakCount;
  };

  const streakLength = calculateStreakLength();
  
  // Get last 7 days
  const getLast7Days = () => {
    const days = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      date.setHours(0, 0, 0, 0);
      
      const streak = streaks?.find(s => {
        const streakDate = new Date(s.date);
        streakDate.setHours(0, 0, 0, 0);
        return streakDate.getTime() === date.getTime();
      });
      
      days.push({
        date,
        duration: streak?.duration || 0,
        dayLetter: "SMTWTFS"[date.getDay()],
      });
    }
    
    return days;
  };
  
  const weekdays = getLast7Days();

  return (
    <div className="bg-neutral-light rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium text-neutral-darker">Your Study Streak</h3>
        <span className="bg-accent text-neutral-darker px-2 py-1 rounded-full text-sm font-bold">
          {streakLength} days
        </span>
      </div>
      <div className="flex justify-between">
        {weekdays.map((day, index) => (
          <div key={index} className="flex flex-col items-center">
            <div 
              className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                day.duration > 0 
                  ? "bg-primary text-white" 
                  : (index === 6 ? "bg-primary bg-opacity-50 text-white" : "bg-neutral-medium text-neutral-dark")
              }`}
            >
              {day.dayLetter}
            </div>
            <span className="text-xs text-neutral-dark">
              {index === 6 
                ? "Today" 
                : (day.duration > 0 ? `${day.duration}m` : "")}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
