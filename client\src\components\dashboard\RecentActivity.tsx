import { useQuery } from "@tanstack/react-query";
import { Activity, Exercise } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";

interface RecentActivityProps {
  userId: number;
}

export function RecentActivity({ userId }: RecentActivityProps) {
  const { data: activities, isLoading: activitiesLoading } = useQuery<Activity[]>({
    queryKey: [`/api/activities/${userId}?limit=3`],
  });

  const { data: exercises, isLoading: exercisesLoading } = useQuery<Exercise[]>({
    queryKey: ["/api/exercises"],
  });

  const isLoading = activitiesLoading || exercisesLoading;

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <Skeleton className="h-7 w-48 mb-4" />
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="border-b border-neutral-medium pb-3 last:border-0 last:pb-0">
              <div className="flex items-start">
                <Skeleton className="h-6 w-6 mr-3 mt-1" />
                <div className="w-full">
                  <Skeleton className="h-5 w-3/4 mb-1" />
                  <Skeleton className="h-4 w-2/3 mb-1" />
                  <Skeleton className="h-3 w-1/4 mt-1" />
                </div>
              </div>
            </div>
          ))}
        </div>
        <Skeleton className="h-6 w-32 mt-4" />
      </div>
    );
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "exercise":
        return "school";
      case "test":
        return "assignment";
      default:
        return "history";
    }
  };

  const getActivityTitle = (activity: Activity) => {
    if (activity.exerciseId && exercises) {
      const exercise = exercises.find(e => e.id === activity.exerciseId);
      if (exercise) {
        const skillName = exercise.skill.charAt(0).toUpperCase() + exercise.skill.slice(1);
        return `${skillName} Exercise: ${exercise.title}`;
      }
    }
    
    return `${activity.type.charAt(0).toUpperCase() + activity.type.slice(1)} Activity`;
  };

  const getActivityDescription = (activity: Activity) => {
    if (activity.score !== null) {
      return `${activity.type === "test" ? "Scored" : "Completed with"} ${activity.score}% ${
        activity.score >= 70 ? "(Passed)" : "(Needs improvement)"
      }`;
    }
    return "Submitted for review";
  };

  const formatActivityDate = (date: Date) => {
    const now = new Date();
    const activityDate = new Date(date);
    
    if (activityDate.toDateString() === now.toDateString()) {
      return `Today, ${format(activityDate, "h:mm a")}`;
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    
    if (activityDate.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${format(activityDate, "h:mm a")}`;
    }
    
    return format(activityDate, "MMM d, yyyy, h:mm a");
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-neutral-darker mb-4">Recent Activity</h2>
      <ul className="space-y-4">
        {activities?.map((activity) => (
          <li key={activity.id} className="border-b border-neutral-medium pb-3 last:border-0 last:pb-0">
            <div className="flex items-start">
              <span className="material-icons text-primary mr-3 mt-1">
                {getActivityIcon(activity.type)}
              </span>
              <div>
                <h4 className="font-medium text-neutral-darker">{getActivityTitle(activity)}</h4>
                <p className="text-sm text-neutral-dark">{getActivityDescription(activity)}</p>
                <p className="text-xs text-neutral-dark mt-1">{formatActivityDate(new Date(activity.completed))}</p>
              </div>
            </div>
          </li>
        ))}
      </ul>
      <button className="text-primary font-medium mt-4 hover:underline flex items-center">
        View all activity
        <span className="material-icons ml-1 text-sm">arrow_forward</span>
      </button>
    </div>
  );
}
