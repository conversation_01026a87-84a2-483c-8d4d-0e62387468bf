import { useQuery } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { SkillCard } from "@/components/skills/SkillCard";
import { ExerciseItem } from "@/components/skills/ExerciseItem";
import { useAuth } from "@/hooks/use-auth";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "wouter";
import { axiosInstance } from "@/lib/queryClient";

export default function Listening() {
  const { user } = useAuth();
  const [location] = useLocation();
  const match = location.match(/\/listening\/(\d+)/);
  // const exerciseId = match ? parseInt(match[1]) : null;

  const { data: exercises, isLoading } = useQuery<any>({
    queryKey: ["/api/exercises?skill=listening"],
    queryFn: async () => {
      const res = await axiosInstance.get("/api/exercises/?skill=listening&uid=your-session-id");
      return res.data;
    },
  });
  console.log('exercises: ', exercises);
  const sampleExercises = [
    {
      id: "english-conversation-1",
      skill: "listening",
      title: "Daily Conversation Practice - A1/A2",
      description: "Listen to common everyday conversations and answer comprehension questions about what you heard.",
      content: {
        imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='300'%3E%3Cdefs%3E%3ClinearGradient id='conversationGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23F59E0B;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23EF4444;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='600' height='300' fill='url(%23conversationGrad)'/%3E%3Ctext x='300' y='130' font-family='Arial' font-size='36' fill='white' text-anchor='middle'%3E🎧 Conversations%3C/text%3E%3Ctext x='300' y='180' font-family='Arial' font-size='20' fill='white' text-anchor='middle' opacity='0.8'%3EDaily English Practice%3C/text%3E%3C/svg%3E",
        exercises: 15
      }
    },
    {
      id: "music-listening-1",
      skill: "listening",
      title: "Song Lyrics Recognition",
      description: "Listen to popular songs and fill in missing lyrics while improving your listening accuracy.",
      content: {
        imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='300'%3E%3Cdefs%3E%3ClinearGradient id='musicGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%2310B981;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%233B82F6;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='600' height='300' fill='url(%23musicGrad)'/%3E%3Ctext x='300' y='120' font-family='Arial' font-size='36' fill='white' text-anchor='middle'%3E🎵 Song Lyrics%3C/text%3E%3Ctext x='300' y='160' font-family='Arial' font-size='18' fill='white' text-anchor='middle' opacity='0.8'%3EFill in the Blanks%3C/text%3E%3C/svg%3E",
        exercises: 12
      }
    },
    {
      id: "news-reports-1",
      skill: "listening",
      title: "News Report Comprehension",
      description: "Listen to short news segments and answer questions about who, what, when, where, and why.",
      content: {
        imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='300'%3E%3Cdefs%3E%3ClinearGradient id='newsGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23DC2626;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23F59E0B;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='600' height='300' fill='url(%23newsGrad)'/%3E%3Ctext x='300' y='120' font-family='Arial' font-size='36' fill='white' text-anchor='middle'%3E📺 News Reports%3C/text%3E%3Ctext x='300' y='160' font-family='Arial' font-size='18' fill='white' text-anchor='middle' opacity='0.8'%3E5W Questions%3C/text%3E%3C/svg%3E",
        exercises: 6
      }
    },
    {
      id: "accent-training-1",
      skill: "listening",
      title: "Different Accents Practice",
      description: "Listen to speakers with various English accents (British, American, Australian) and improve comprehension.",
      content: {
        imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='300'%3E%3Cdefs%3E%3ClinearGradient id='accentGrad' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%2306B6D4;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%2310B981;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='600' height='300' fill='url(%23accentGrad)'/%3E%3Ctext x='300' y='120' font-family='Arial' font-size='32' fill='white' text-anchor='middle'%3E🌍 World Accents%3C/text%3E%3Ctext x='300' y='160' font-family='Arial' font-size='18' fill='white' text-anchor='middle' opacity='0.8'%3EBritish • American • Australian%3C/text%3E%3C/svg%3E",
        exercises: 18
      }
    }
  ];

  const { data: skills } = useQuery({
    queryKey: [`/api/skills/${user?.id}`],
    enabled: !!user,
  });

  const listeningSkill = (skills as any)?.find((skill: any) => skill.type === "listening");

  const proficiency = listeningSkill?.proficiency || 0;
  const lastActivity = listeningSkill ? new Date(listeningSkill.lastActivity) : new Date();

  const getLastActivityText = (date: Date) => {
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    return `${diffDays} days ago`;
  };

  return (
    <SkillCard
      title="Listening Skills"
      description="Improve your German listening comprehension"
      icon={<span className="material-icons mr-2 text-primary">headphones</span>}
      proficiency={proficiency}
      lastActivity={getLastActivityText(lastActivity)}
    >

      <h3 className="font-bold text-neutral-darker text-lg mb-4">Listening Exercise Types</h3>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sampleExercises?.map((exercise: any, index) => (
            <ExerciseItem key={exercise.id} exercise={exercise} index={index} />
          ))}
        </div>
      )}

    </SkillCard>
  );
}
