import { useQuery } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { SkillCard } from "@/components/skills/SkillCard";
import { ExerciseItem } from "@/components/skills/ExerciseItem";
import { ReadingExercise } from "@/components/skills/ReadingExercise";
import { useAuth } from "@/hooks/use-auth";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "wouter";

export default function Reading() {
  const { user } = useAuth();
  const [location] = useLocation();
  const match = location.match(/\/reading\/(\d+)/);
  const exerciseId = match ? parseInt(match[1]) : null;

  const { data: exercises, isLoading } = useQuery<Exercise[]>({
    queryKey: ["/api/exercises?skill=reading"],
  });

  const { data: skills } = useQuery({
    queryKey: [`/api/skills/${user?.id}`],
    enabled: !!user,
  });

  const readingSkill = skills?.find(skill => skill.type === "reading");
  
  const proficiency = readingSkill?.proficiency || 0;
  const lastActivity = readingSkill ? new Date(readingSkill.lastActivity) : new Date();
  
  const getLastActivityText = (date: Date) => {
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    return `${diffDays} days ago`;
  };

  return (
    <SkillCard
      title="Reading Skills"
      description="Improve your German reading comprehension"
      icon={<span className="material-icons mr-2 text-primary">menu_book</span>}
      proficiency={proficiency}
      lastActivity={getLastActivityText(lastActivity)}
    >
      {exerciseId ? (
        <ReadingExercise exerciseId={exerciseId} />
      ) : (
        <>
          <h3 className="font-bold text-neutral-darker text-lg mb-4">Reading Exercise Types</h3>
          
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-64 w-full" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {exercises?.map(exercise => (
                <ExerciseItem key={exercise.id} exercise={exercise} />
              ))}
            </div>
          )}
        </>
      )}
    </SkillCard>
  );
}
