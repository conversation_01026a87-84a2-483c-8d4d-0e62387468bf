import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { User } from "@shared/schema";

export function useAuth() {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

  const { 
    data: user, 
    isLoading, 
    error 
  } = useQuery<User>({ 
    queryKey: ["/api/user"],
    retry: false,
  });

  useEffect(() => {
    if (user) {
      setIsLoggedIn(true);
    } else if (error) {
      setIsLoggedIn(false);
    }
  }, [user, error]);

  const logout = () => {
    // In a real app, this would call an API endpoint to log the user out
    setIsLoggedIn(false);
  };

  return {
    user,
    isLoading,
    isLoggedIn,
    logout
  };
}
