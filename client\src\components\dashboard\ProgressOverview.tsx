import { useQuery } from "@tanstack/react-query";
import { Skill } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";

interface ProgressOverviewProps {
  userId: number;
}

export function ProgressOverview({ userId }: ProgressOverviewProps) {
  const { data: skills, isLoading } = useQuery<Skill[]>({
    queryKey: [`/api/skills/${userId}`],
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-neutral-light rounded-lg p-4 flex flex-col">
            <div className="flex justify-between items-center mb-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-12" />
            </div>
            <Skeleton className="h-2 w-full mb-2" />
            <Skeleton className="h-4 w-36 mt-auto" />
          </div>
        ))}
      </div>
    );
  }

  const getLastActivityText = (date: Date) => {
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    return `${diffDays} days ago`;
  };

  const getSkillName = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {skills?.map((skill) => (
        <div key={skill.id} className="bg-neutral-light rounded-lg p-4 flex flex-col">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium text-neutral-darker">{getSkillName(skill.type)}</h3>
            <span className="text-primary font-bold">{skill.proficiency}%</span>
          </div>
          <div className="w-full bg-neutral-medium rounded-full h-2 mb-2">
            <div 
              className="bg-primary rounded-full h-2 progress-bar" 
              style={{ width: `${skill.proficiency}%` }}
            ></div>
          </div>
          <p className="text-sm text-neutral-dark mt-auto">
            Last activity: {getLastActivityText(new Date(skill.lastActivity))}
          </p>
        </div>
      ))}
    </div>
  );
}
