import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";

export function Header() {
  const { user } = useAuth();
  const [location] = useLocation();

  const navItems = [
    { name: "Dashboard", path: "/" },
    { name: "Practice", path: "/practice" },
    { name: "Mock Tests", path: "/mock-tests" },
    { name: "Progress", path: "/progress" }
  ];

  return (
    <header className="bg-white shadow-sm px-4 py-3">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        {/* Logo */}
        <Link href="/">
          <div className="flex items-center cursor-pointer">
            <div className="bg-primary w-10 h-10 rounded-lg flex items-center justify-center text-white mr-2">
              <span className="material-icons">school</span>
            </div>
            <span className="font-bold text-lg text-neutral-800">DeutschMeister</span>
          </div>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex space-x-6">
          {navItems.map((item) => (
            <Link key={item.path} href={item.path}>
              <span className={`py-1 px-1 font-medium text-sm cursor-pointer ${
                location === item.path 
                  ? "text-primary border-b-2 border-primary" 
                  : "text-neutral-600 hover:text-primary"
              }`}>
                {item.name}
              </span>
            </Link>
          ))}
        </nav>

        {/* User Section */}
        <div className="flex items-center space-x-4">
          {/* Level Badge */}
          <div className="bg-accent text-white text-xs font-medium px-3 py-1 rounded-full hidden sm:flex items-center">
            <span>Level:</span>
            <span className="ml-1 font-bold">A2</span>
          </div>
          
          {/* User Avatar */}
          <div className="relative">
            <Avatar className="h-9 w-9 cursor-pointer">
              <AvatarImage src="/avatar.png" alt={user?.firstName} />
              <AvatarFallback className="bg-primary-light text-white">
                {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </header>
  );
}