import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { ZodError } from "zod";
import { fromZodError } from "zod-validation-error";
import {
  insertUserSchema,
  insertSkillSchema,
  insertStreakSchema,
  insertExerciseSchema,
  insertActivitySchema,
  insertMockTestSchema,
  insertTestAttemptSchema,
  insertSpeakingTopicSchema,
  skillType
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Error handling middleware for Zod validation
  const validateBody = (schema: any) => (req: Request, res: Response, next: any) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const formattedError = fromZodError(error);
        res.status(400).json({ message: formattedError.message });
      } else {
        res.status(400).json({ message: "Invalid request body" });
      }
    }
  };

  // Define API routes
  app.get("/api/user", async (req, res) => {
    // Demo user for frontend development
    const user = await storage.getUserByUsername("maria.schmidt");
    if (user) {
      // Don't send password to client
      const { password, ...userWithoutPassword } = user;
      res.json(userWithoutPassword);
    } else {
      res.status(404).json({ message: "User not found" });
    }
  });

  // Skills endpoints
  app.get("/api/skills/:userId", async (req, res) => {
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const skills = await storage.getUserSkills(userId);
    res.json(skills);
  });

  app.post("/api/skills", validateBody(insertSkillSchema), async (req, res) => {
    try {
      const { userId, type, proficiency } = req.body;
      
      // Validate skill type
      if (!skillType.safeParse(type).success) {
        return res.status(400).json({ message: "Invalid skill type" });
      }
      
      const skill = await storage.updateUserSkill(userId, type, proficiency);
      res.json(skill);
    } catch (error) {
      res.status(500).json({ message: "Failed to update skill" });
    }
  });

  // Streaks endpoints
  app.get("/api/streaks/:userId", async (req, res) => {
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const streaks = await storage.getUserStreaks(userId);
    res.json(streaks);
  });

  app.post("/api/streaks", validateBody(insertStreakSchema), async (req, res) => {
    try {
      const streak = await storage.addUserStreak(req.body);
      res.json(streak);
    } catch (error) {
      res.status(500).json({ message: "Failed to add streak" });
    }
  });

  // Exercises endpoints
  app.get("/api/exercises", async (req, res) => {
    const skill = req.query.skill as string;
    const level = req.query.level as string;
    
    if (skill) {
      if (!skillType.safeParse(skill).success) {
        return res.status(400).json({ message: "Invalid skill type" });
      }
      
      const exercises = await storage.getExercisesBySkill(skill, level);
      res.json(exercises);
    } else {
      const exercises = await storage.getExercises();
      res.json(exercises);
    }
  });

  app.get("/api/exercises/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: "Invalid exercise ID" });
    }

    const exercise = await storage.getExercise(id);
    if (exercise) {
      res.json(exercise);
    } else {
      res.status(404).json({ message: "Exercise not found" });
    }
  });

  app.post("/api/exercises", validateBody(insertExerciseSchema), async (req, res) => {
    try {
      const exercise = await storage.createExercise(req.body);
      res.json(exercise);
    } catch (error) {
      res.status(500).json({ message: "Failed to create exercise" });
    }
  });

  // Activities endpoints
  app.get("/api/activities/:userId", async (req, res) => {
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
    const activities = await storage.getUserActivities(userId, limit);
    res.json(activities);
  });

  app.post("/api/activities", validateBody(insertActivitySchema), async (req, res) => {
    try {
      const activity = await storage.createActivity(req.body);
      res.json(activity);
    } catch (error) {
      res.status(500).json({ message: "Failed to create activity" });
    }
  });

  // Mock tests endpoints
  app.get("/api/mock-tests", async (req, res) => {
    const tests = await storage.getMockTests();
    res.json(tests);
  });

  app.get("/api/mock-tests/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: "Invalid test ID" });
    }

    const test = await storage.getMockTest(id);
    if (test) {
      res.json(test);
    } else {
      res.status(404).json({ message: "Mock test not found" });
    }
  });

  app.post("/api/mock-tests", validateBody(insertMockTestSchema), async (req, res) => {
    try {
      const test = await storage.createMockTest(req.body);
      res.json(test);
    } catch (error) {
      res.status(500).json({ message: "Failed to create mock test" });
    }
  });

  // Test attempts endpoints
  app.get("/api/test-attempts/:userId", async (req, res) => {
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    const attempts = await storage.getUserTestAttempts(userId);
    res.json(attempts);
  });

  app.post("/api/test-attempts", validateBody(insertTestAttemptSchema), async (req, res) => {
    try {
      const attempt = await storage.createTestAttempt(req.body);
      res.json(attempt);
    } catch (error) {
      res.status(500).json({ message: "Failed to create test attempt" });
    }
  });

  // Speaking topics endpoints
  app.get("/api/speaking-topics", async (req, res) => {
    const topics = await storage.getSpeakingTopics();
    res.json(topics);
  });

  app.get("/api/speaking-topics/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: "Invalid topic ID" });
    }

    const topic = await storage.getSpeakingTopic(id);
    if (topic) {
      res.json(topic);
    } else {
      res.status(404).json({ message: "Speaking topic not found" });
    }
  });

  app.post("/api/speaking-topics", validateBody(insertSpeakingTopicSchema), async (req, res) => {
    try {
      const topic = await storage.createSpeakingTopic(req.body);
      res.json(topic);
    } catch (error) {
      res.status(500).json({ message: "Failed to create speaking topic" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
