import { useQuery } from "@tanstack/react-query";
import { MockTest } from "@shared/schema";
import { MockTestCard } from "@/components/tests/MockTestCard";
import { TestHistoryTable } from "@/components/tests/TestHistoryTable";
import { useAuth } from "@/hooks/use-auth";
import { Skeleton } from "@/components/ui/skeleton";

export default function MockTests() {
  const { user } = useAuth();
  
  const { data: tests, isLoading } = useQuery<MockTest[]>({
    queryKey: ["/api/mock-tests"],
  });

  return (
    <section id="mockTests" className="mb-8">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-neutral-darker flex items-center">
              <span className="material-icons mr-2 text-primary">assignment</span>
              Mock Tests
            </h2>
            <p className="text-neutral-dark mt-1">Practice with full exam simulations</p>
          </div>
        </div>
        
        {/* Available Tests */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <Skeleton key={i} className="h-64 w-full" />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {tests?.map(test => (
              <MockTestCard key={test.id} test={test} />
            ))}
          </div>
        )}
        
        {/* Individual Skill Tests */}
        <h3 className="font-bold text-neutral-darker text-lg mt-8 mb-4">Practice by Skill</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="border border-neutral-medium rounded-lg p-4 hover:shadow-md transition cursor-pointer">
            <div className="flex items-center mb-3">
              <span className="material-icons text-primary mr-2">menu_book</span>
              <h4 className="font-medium text-neutral-darker">A2 Reading</h4>
            </div>
            <p className="text-sm text-neutral-dark mb-3">25 minutes, 3 texts with comprehension questions</p>
            <button className="w-full bg-primary text-white rounded py-1.5 text-sm hover:bg-opacity-90 transition">Start</button>
          </div>
          
          <div className="border border-neutral-medium rounded-lg p-4 hover:shadow-md transition cursor-pointer">
            <div className="flex items-center mb-3">
              <span className="material-icons text-primary mr-2">edit</span>
              <h4 className="font-medium text-neutral-darker">A2 Writing</h4>
            </div>
            <p className="text-sm text-neutral-dark mb-3">20 minutes, 2 writing tasks with guided prompts</p>
            <button className="w-full bg-primary text-white rounded py-1.5 text-sm hover:bg-opacity-90 transition">Start</button>
          </div>
          
          <div className="border border-neutral-medium rounded-lg p-4 hover:shadow-md transition cursor-pointer">
            <div className="flex items-center mb-3">
              <span className="material-icons text-primary mr-2">headphones</span>
              <h4 className="font-medium text-neutral-darker">A2 Listening</h4>
            </div>
            <p className="text-sm text-neutral-dark mb-3">25 minutes, 3 audio recordings with questions</p>
            <button className="w-full bg-primary text-white rounded py-1.5 text-sm hover:bg-opacity-90 transition">Start</button>
          </div>
          
          <div className="border border-neutral-medium rounded-lg p-4 hover:shadow-md transition cursor-pointer">
            <div className="flex items-center mb-3">
              <span className="material-icons text-primary mr-2">record_voice_over</span>
              <h4 className="font-medium text-neutral-darker">A2 Speaking</h4>
            </div>
            <p className="text-sm text-neutral-dark mb-3">20 minutes, conversation practice with AI avatar</p>
            <button className="w-full bg-primary text-white rounded py-1.5 text-sm hover:bg-opacity-90 transition">Start</button>
          </div>
        </div>
        
        {/* Test History */}
        <h3 className="font-bold text-neutral-darker text-lg mt-8 mb-4">Recent Test Results</h3>
        {user && <TestHistoryTable userId={user.id} />}
      </div>
    </section>
  );
}
