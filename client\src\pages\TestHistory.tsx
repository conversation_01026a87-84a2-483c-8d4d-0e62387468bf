import { useAuth } from "@/hooks/use-auth";
import { TestHistoryTable } from "@/components/tests/TestHistoryTable";
import { Skeleton } from "@/components/ui/skeleton";

export default function TestHistory() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <Skeleton className="h-8 w-48 mb-2" />
        <Skeleton className="h-5 w-64 mb-6" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!user) {
    return <div>Please log in to view your test history</div>;
  }

  return (
    <section id="testHistory" className="mb-8">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-neutral-darker flex items-center">
              <span className="material-icons mr-2 text-primary">history</span>
              Test History
            </h2>
            <p className="text-neutral-dark mt-1">Review your past test attempts and track your progress</p>
          </div>
        </div>
        
        <TestHistoryTable userId={user.id} />
      </div>
    </section>
  );
}
