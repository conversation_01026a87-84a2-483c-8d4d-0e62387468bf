import { Link } from "wouter";
import { Exercise } from "@shared/schema";

interface ExerciseItemProps {
  exercise: Exercise;
  index: number;
}

export function ExerciseItem({ exercise, index }: ExerciseItemProps) {
  const { id, title, description, content } = exercise;

  return (
    <div className={`relative ${exercise.skill === "listening" ? index > 0 ? "opacity-40 cursor-not-allowed" : "opacity-100" : ""} border bordex r-neutral-medium rounded-xl overflow-hidden hover:shadow-md transition cursor-pointer `}>
      <img
        src={(content as any)!.imageUrl || "https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300"}
        alt={title}
        className="w-full h-40 object-cover"
      />
      {(index > 0 && exercise.skill === "listening") && <div>
          <div className="absolute top-24 left-0 bg-neutral-dark bg-opacity-100 w-full h-full flex justify-center">
            <span className="text-white text-2xl font-bold !opacity-100">
              Coming Soon
            </span>
          </div>
        </div>
      }
      <div className={`p-4`}>
        <h3 className="font-medium text-neutral-darker mb-1">{title}</h3>
        <p className="text-sm text-neutral-dark mb-3">{description}</p>
        <div className="flex justify-between items-center">
          <span className="text-xs bg-blue-100 text-primary px-2 py-1 rounded-full">
            {(content as any).exercises || 0} exercises
          </span>
          <Link href={`/${exercise.skill}/${id}`}>
            <button className="text-primary hover:text-opacity-80">
              <span className="material-icons">arrow_forward</span>
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}
