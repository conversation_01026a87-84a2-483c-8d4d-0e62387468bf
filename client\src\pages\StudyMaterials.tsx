import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export default function StudyMaterials() {
  const studyMaterials = [
    {
      id: 1,
      title: "Grammar Basics",
      description: "Essential German grammar rules for A2/B1 level",
      imageUrl: "https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
      topics: ["Articles", "Verb Conjugation", "Cases", "Word Order"]
    },
    {
      id: 2,
      title: "Vocabulary Lists",
      description: "Common words and phrases for everyday situations",
      imageUrl: "https://images.unsplash.com/photo-1544980919-e17526d4ed0a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
      topics: ["Daily Life", "Travel", "Food & Drink", "Hobbies"]
    },
    {
      id: 3,
      title: "Pronunciation Guide",
      description: "Improve your German pronunciation with audio examples",
      imageUrl: "https://images.unsplash.com/photo-1516354597205-250d9a889962?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
      topics: ["Vowels", "Consonants", "Word Stress", "Sentence Intonation"]
    },
    {
      id: 4,
      title: "Common Phrases",
      description: "Essential expressions for everyday conversations",
      imageUrl: "https://images.unsplash.com/photo-1523730205978-59fd1b2965e3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
      topics: ["Greetings", "Shopping", "Restaurant", "Asking for Directions"]
    }
  ];

  return (
    <section id="studyMaterials" className="mb-8">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-neutral-darker flex items-center">
              <span className="material-icons mr-2 text-primary">folder</span>
              Study Materials
            </h2>
            <p className="text-neutral-dark mt-1">Reference materials to support your exam preparation</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {studyMaterials.map((material) => (
            <Card key={material.id}>
              <CardHeader className="p-0">
                <img 
                  src={material.imageUrl} 
                  alt={material.title} 
                  className="w-full h-40 object-cover rounded-t-lg"
                />
              </CardHeader>
              <CardContent className="p-4">
                <CardTitle className="text-lg font-medium mb-2">{material.title}</CardTitle>
                <CardDescription className="text-neutral-dark mb-4">{material.description}</CardDescription>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {material.topics.map((topic, index) => (
                    <span 
                      key={index} 
                      className="text-xs bg-blue-100 text-primary px-2 py-1 rounded-full"
                    >
                      {topic}
                    </span>
                  ))}
                </div>
                
                <Button variant="outline" className="w-full">View Material</Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
