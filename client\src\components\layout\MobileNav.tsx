import { Link, useLocation } from "wouter";

export function MobileNav() {
  const [location] = useLocation();

  return (
    <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-neutral-medium px-2 py-1 z-50">
      <div className="flex justify-around">
        <Link 
          href="/"
          className={`flex flex-col items-center py-1 px-3 ${
            location === "/" ? "text-primary" : "text-neutral-dark"
          }`}
        >
          <span className="material-icons">dashboard</span>
          <span className="text-xs">Dashboard</span>
        </Link>
        <Link 
          href="/skills"
          className={`flex flex-col items-center py-1 px-3 ${
            location.includes("/reading") || 
            location.includes("/writing") || 
            location.includes("/listening") || 
            location.includes("/speaking") 
              ? "text-primary" 
              : "text-neutral-dark"
          }`}
        >
          <span className="material-icons">school</span>
          <span className="text-xs">Skills</span>
        </Link>
        <Link 
          href="/mock-tests"
          className={`flex flex-col items-center py-1 px-3 ${
            location.includes("/mock-tests") || location.includes("/test-history")
              ? "text-primary" 
              : "text-neutral-dark"
          }`}
        >
          <span className="material-icons">assignment</span>
          <span className="text-xs">Tests</span>
        </Link>
        <Link 
          href="/settings"
          className={`flex flex-col items-center py-1 px-3 ${
            location === "/settings" ? "text-primary" : "text-neutral-dark"
          }`}
        >
          <span className="material-icons">person</span>
          <span className="text-xs">Profile</span>
        </Link>
      </div>
    </nav>
  );
}
