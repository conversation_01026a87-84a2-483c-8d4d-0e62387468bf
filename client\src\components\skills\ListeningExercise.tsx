import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { CheckCircle, XCircle, Volume2, Play, Pause, RotateCcw } from "lucide-react";
import { Skeleton } from "../ui/skeleton";
import { AudioVisualizer } from 'react-audio-visualize';
import { axiosInstance } from "@/lib/queryClient";

interface ListeningExerciseProps {
  exercise: any;
  isLoading: boolean;
}

export function ListeningExercise({ exercise, isLoading }: ListeningExerciseProps) {
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);

  // Audio player state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);

  const audioRef = useRef<HTMLAudioElement>(null);
  const visualizerRef = useRef<HTMLCanvasElement>(null);

  // Convert base64 audio to blob and create audio URL
  useEffect(() => {
    if (exercise?.ex_audio) {
      try {
        const binaryString = atob(exercise.ex_audio);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        // Create blob
        const blob = new Blob([bytes], { type: 'audio/mpeg' });
        setAudioBlob(blob);

        const audioUrl = URL.createObjectURL(blob);
        if (audioRef.current) {
          audioRef.current.src = audioUrl;
          setIsAudioLoaded(true);
        }

        // Cleanup URL on unmount
        return () => URL.revokeObjectURL(audioUrl);
      } catch (err) {
        console.error('Error processing audio data:', err);
      }
    }
  }, [exercise?.ex_audio]);

  // Audio event handlers
  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  // Audio controls
  const togglePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const resetAudio = () => {
    if (!audioRef.current) return;
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
    setIsPlaying(false);
  };

  const handleSliderChange = (value: number[]) => {
    if (!audioRef.current) return;
    const newTime = value[0];
    audioRef.current.currentTime = newTime;
    audioRef.current.pause();
    setIsPlaying(false);
    setCurrentTime(newTime);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (option: string) => {
    if (isSubmitted) return;

    setSelectedAnswers(prev => {
      if (prev.includes(option)) {
        return prev.filter(ans => ans !== option);
      } else {
        return [...prev, option];
      }
    });
  };

  const handleSubmit = () => {
    if (selectedAnswers.length === 0) return;

    setIsSubmitted(true);
    setShowResults(true);

    // Check if answers are correct
    const correctAnswers = Array.isArray(exercise.correct_ans)
      ? exercise.correct_ans
      : [exercise.correct_ans];

    const isAnswerCorrect = selectedAnswers.length === correctAnswers.length &&
      selectedAnswers.every(ans => correctAnswers.includes(ans));

    setIsCorrect(isAnswerCorrect);
  };

  const handleNext = async () => {
    // Reset state for next exercise
    setSelectedAnswers([]);
    setIsSubmitted(false);
    setShowResults(false);
    setIsCorrect(false);
    resetAudio();
    const res = await axiosInstance.get(`/api/question/1?skill=listening&uid=test-user-123`);
    exercise = res.data
  };

  const getOptionStatus = (option: string) => {
    if (!showResults) return 'default';

    const correctAnswers = Array.isArray(exercise.correct_ans)
      ? exercise.correct_ans
      : [exercise.correct_ans];

    const isCorrectOption = correctAnswers.includes(option);
    const isSelected = selectedAnswers.includes(option);

    if (isSelected && isCorrectOption) return 'correct';
    if (isSelected && !isCorrectOption) return 'wrong';
    if (!isSelected && isCorrectOption) return 'missed';
    return 'default';
  };

  const getOptionIcon = (option: string) => {
    const status = getOptionStatus(option);
    if (status === 'correct') return <CheckCircle className="w-5 h-5 text-green-600" />;
    if (status === 'wrong') return <XCircle className="w-5 h-5 text-red-600" />;
    if (status === 'missed') return <CheckCircle className="w-5 h-5 text-green-600" />;
    return null;
  };

  const getOptionClassName = (option: string) => {
    const status = getOptionStatus(option);
    const baseClasses = "w-full p-4 text-left border-2 rounded-lg transition-all duration-200 flex items-center justify-between";

    if (!showResults) {
      const isSelected = selectedAnswers.includes(option);
      return `${baseClasses} ${isSelected
        ? "border-blue-500 bg-blue-50 text-blue-700"
        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
        }`;
    }

    switch (status) {
      case 'correct':
        return `${baseClasses} border-green-500 bg-green-50 text-green-700`;
      case 'wrong':
        return `${baseClasses} border-red-500 bg-red-50 text-red-700`;
      case 'missed':
        return `${baseClasses} border-green-500 bg-green-100 text-green-700`;
      default:
        return `${baseClasses} border-gray-200 bg-gray-50 text-gray-600`;
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="space-y-2">
              {[1, 2, 3, 4].map(i => (
                <Skeleton key={i} className="h-12 w-full rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!exercise || Object.keys(exercise).length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
          <p className="text-gray-600">Exercise not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className=" p-6 space-y-6 max-w-max">
      <div className="bg-white  shadow-sm w-full">
        {/* Header */}
        <div className="p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center gap-3 mb-4">
            <Volume2 className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Listening Exercise</h2>
          </div>
          <p className="text-gray-700 text-lg">{exercise.q_text}</p>
        </div>

        <div className="p-6 border-b bg-gray-50">
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Listen to the audio:</h3>
          </div>
          <div className="bg-white rounded-lg border p-4">
            {/* Audio Element */}
            <audio
              ref={audioRef}
              onLoadedMetadata={handleLoadedMetadata}
              onTimeUpdate={handleTimeUpdate}
              onEnded={handleEnded}
              className="hidden"
            />

            {/* Audio Visualizer */}
            {audioBlob && (
              <div className="mb-4 w-full">
                <AudioVisualizer
                  ref={visualizerRef}
                  blob={audioBlob}
                  width={900}
                  height={120}
                  barWidth={2}
                  gap={1}
                  barColor="gray"
                  barPlayedColor="#2b14fa"
                  currentTime={currentTime}
                  style={{
                    width: '100%',
                  }}
                />
              </div>
            )}

            {/* Controls */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button
                  onClick={togglePlayPause}
                  disabled={!isAudioLoaded}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2 w-24"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  {isPlaying ? 'Pause' : 'Play'}
                </Button>

                <Button
                  onClick={resetAudio}
                  disabled={!isAudioLoaded}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  Reset
                </Button>
              </div>

              {/* Slider and Time Display */}
              <div className="flex-1 flex items-center gap-2">
                <span className="text-sm text-gray-600 min-w-12">
                  {formatTime(currentTime)}
                </span>
                <Slider
                  value={[currentTime]}
                  max={duration}
                  step={0.1}
                  onValueChange={handleSliderChange}
                  disabled={!isAudioLoaded}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 min-w-12">
                  {formatTime(duration)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Answer Options */}
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-800 mb-4">
            Choose your answer{Array.isArray(exercise.correct_ans) ? 's' : ''}:
          </h3>
          <div className="space-y-3">
            {exercise.options.map((option: any, index: any) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(option)}
                disabled={isSubmitted}
                className={getOptionClassName(option)}
              >
                <span className="text-left flex-1">{option}</span>
                {getOptionIcon(option)}
              </button>
            ))}
          </div>
        </div>

        {/* Results Message */}
        {showResults && (
          <div className="px-6 pb-6">
            <div className={`p-4 rounded-lg border ${isCorrect
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
              }`}>
              <div className="flex items-center gap-2">
                {isCorrect ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className="font-medium">
                  {isCorrect ? '🎉 Correct! Well done!' : '❌ Not quite right. Check the highlighted answers above. OR 🪄 Ask Maity'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="p-6 border-t bg-gray-50 flex justify-between">
          <div className="text-sm text-gray-600">
            Selected: {selectedAnswers.length} answer{selectedAnswers.length !== 1 ? 's' : ''}
          </div>
          <div className="flex gap-3 flexwr">
            <Button
              onClick={handleSubmit}
              disabled={selectedAnswers.length === 0 || isSubmitted}
              className="px-6 text-white rounded-[.5rem]"
            >
              Submit Answer
            </Button>
            <Button
              variant={'secondary'}
              onClick={handleNext}
              disabled={!isSubmitted}
              className={`px-6 text-white rounded-[.5rem] bg-[#ffcc33] hover:bg-yellow-300`}
            >
              Next Exercise →
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}