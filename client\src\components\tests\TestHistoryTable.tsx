import { useQuery } from "@tanstack/react-query";
import { TestAttempt, MockTest } from "@shared/schema";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

interface TestHistoryTableProps {
  userId: number;
}

export function TestHistoryTable({ userId }: TestHistoryTableProps) {
  const { data: attempts, isLoading: attemptsLoading } = useQuery<TestAttempt[]>({
    queryKey: [`/api/test-attempts/${userId}`],
  });

  const { data: tests, isLoading: testsLoading } = useQuery<MockTest[]>({
    queryKey: ["/api/mock-tests"],
  });

  const isLoading = attemptsLoading || testsLoading;

  if (isLoading) {
    return (
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border border-neutral-medium">
          <thead>
            <tr className="bg-neutral-light border-b border-neutral-medium">
              <th className="py-3 px-4 text-left text-neutral-darker font-medium">Test Name</th>
              <th className="py-3 px-4 text-left text-neutral-darker font-medium">Date</th>
              <th className="py-3 px-4 text-left text-neutral-darker font-medium">Score</th>
              <th className="py-3 px-4 text-left text-neutral-darker font-medium">Result</th>
              <th className="py-3 px-4 text-left text-neutral-darker font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {[...Array(3)].map((_, i) => (
              <tr key={i} className="border-b border-neutral-medium">
                <td className="py-3 px-4"><Skeleton className="h-5 w-32" /></td>
                <td className="py-3 px-4"><Skeleton className="h-5 w-24" /></td>
                <td className="py-3 px-4"><Skeleton className="h-5 w-12" /></td>
                <td className="py-3 px-4"><Skeleton className="h-5 w-16" /></td>
                <td className="py-3 px-4"><Skeleton className="h-5 w-16" /></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }

  if (!attempts || !tests) {
    return <div>No test history available</div>;
  }

  const getTestName = (testId: number) => {
    const test = tests.find(t => t.id === testId);
    return test ? test.title : "Unknown Test";
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-neutral-medium">
        <thead>
          <tr className="bg-neutral-light border-b border-neutral-medium">
            <th className="py-3 px-4 text-left text-neutral-darker font-medium">Test Name</th>
            <th className="py-3 px-4 text-left text-neutral-darker font-medium">Date</th>
            <th className="py-3 px-4 text-left text-neutral-darker font-medium">Score</th>
            <th className="py-3 px-4 text-left text-neutral-darker font-medium">Result</th>
            <th className="py-3 px-4 text-left text-neutral-darker font-medium">Actions</th>
          </tr>
        </thead>
        <tbody>
          {attempts.map((attempt) => (
            <tr key={attempt.id} className="border-b border-neutral-medium">
              <td className="py-3 px-4 text-neutral-darker">{getTestName(attempt.testId)}</td>
              <td className="py-3 px-4 text-neutral-dark">
                {format(new Date(attempt.completed), "MMM d, yyyy")}
              </td>
              <td className="py-3 px-4 text-neutral-darker font-medium">{attempt.score}%</td>
              <td className="py-3 px-4">
                <span className={`text-xs ${
                  attempt.passed 
                    ? "bg-success text-white" 
                    : "bg-error text-white"
                  } px-2 py-1 rounded-full`}
                >
                  {attempt.passed ? "Passed" : "Failed"}
                </span>
              </td>
              <td className="py-3 px-4">
                <button className="text-primary hover:text-opacity-80">Review</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
