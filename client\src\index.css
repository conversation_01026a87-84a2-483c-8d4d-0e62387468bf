@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Main brand colors */
  --primary: 246 96% 53%; /* #5E5CFF - primary purple */
  --primary-light: 246 96% 63%; /* #7E7CFF */
  --primary-dark: 246 96% 43%; /* #3E3CDF */
  
  /* Skill-specific colors */
  --reading-color: 210 90% 60%; /* #4C8FE6 - blue for reading */
  --writing-color: 142 60% 50%; /* #4AD06D - green for writing */
  --listening-color: 246 80% 60%; /* #7C74FF - purple for listening */
  --speaking-color: 28 90% 60%; /* #FF9144 - orange for speaking */
  
  /* UI colors */
  --accent: 45 100% 60%; /* #FFCC33 - gold/yellow accent */
  --success: 142 65% 50%; /* #3DD86C - success green */
  --warning: 38 95% 60%; /* #FFB52E - warning orange */
  --error: 358 85% 60%; /* #FF5252 - error red */
  
  /* Neutral colors */
  --bg-light: 220 30% 96%; /* #F3F6FB - light background */
  --card-bg: 0 0% 100%; /* #FFFFFF - card background */
  --neutral-50: 220 20% 98%; /* #F9FAFC */
  --neutral-100: 220 15% 95%; /* #F1F3F7 */
  --neutral-200: 220 15% 90%; /* #E2E7F0 */
  --neutral-300: 220 12% 80%; /* #C5CDD9 */
  --neutral-400: 220 10% 65%; /* #A1ADC0 */
  --neutral-500: 220 10% 50%; /* #7A8599 */
  --neutral-600: 220 10% 40%; /* #616D7E */
  --neutral-700: 220 15% 30%; /* #45505E */
  --neutral-800: 220 20% 20%; /* #2C3440 */
  --neutral-900: 220 25% 10%; /* #161C27 */
}

/* Dark mode colors */
.dark {
  --primary: 246 70% 60%; /* #7674FF - slightly softer purple */
  --primary-light: 246 70% 70%; /* #9594FF */
  --primary-dark: 246 70% 50%; /* #5754FF */
  
  /* Skill-specific colors - slightly desaturated for dark mode */
  --reading-color: 210 70% 55%; /* #4A85D6 */
  --writing-color: 142 50% 45%; /* #3AB85E */
  --listening-color: 246 65% 55%; /* #7470E0 */
  --speaking-color: 28 75% 55%; /* #E68A45 */
  
  --accent: 45 85% 55%; /* #E6B800 */
  --success: 142 55% 45%; /* #35C061 */
  --warning: 38 85% 55%; /* #E6A228 */
  --error: 358 75% 55%; /* #E64D4D */
  
  /* Dark mode neutrals */
  --bg-light: 220 25% 15%; /* #232836 */
  --card-bg: 220 25% 18%; /* #293041 */
  --neutral-900: 220 15% 95%; /* #F1F3F7 */
  --neutral-800: 220 15% 85%; /* #D9DEE9 */
  --neutral-700: 220 12% 75%; /* #B9C2D0 */
  --neutral-600: 220 10% 65%; /* #A1ADC0 */
  --neutral-500: 220 10% 50%; /* #7A8599 */
  --neutral-400: 220 10% 40%; /* #616D7E */
  --neutral-300: 220 15% 30%; /* #45505E */
  --neutral-200: 220 20% 20%; /* #2C3440 */
  --neutral-100: 220 25% 15%; /* #232836 */
  --neutral-50: 220 30% 10%; /* #171B25 */
}

/* Apply utility classes */
@layer utilities {
  .bg-primary-gradient {
    background: linear-gradient(120deg, #5E5CFF, #7E7CFF);
  }
  
  .bg-reading {
    background-color: hsl(var(--reading-color));
  }
  
  .bg-writing {
    background-color: hsl(var(--writing-color));
  }
  
  .bg-listening {
    background-color: hsl(var(--listening-color));
  }
  
  .bg-speaking {
    background-color: hsl(var(--speaking-color));
  }
}

/* Global styles */
body {
  font-family: 'Inter', 'Roboto', sans-serif;
  background-color: hsl(var(--bg-light));
  color: hsl(var(--neutral-800));
}

/* Progress bar animation */
.progress-bar {
  transition: width 1s ease-in-out;
}

/* Skill cards */
.skill-card {
  background-color: hsl(var(--card-bg));
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.skill-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}

.float-animation {
  animation: float 4s ease-in-out infinite;
}

/* Avatar styling */
.ai-avatar {
  background-color: #0CAD7A;
  color: white;
  border-radius: 9999px;
  padding: 1rem;
}

/* Custom card styling */
.quick-start-card {
  border-radius: 1rem;
  height: 100%;
  transition: transform 0.2s ease;
}

.quick-start-card:hover {
  transform: translateY(-4px);
}

/* German flag gradient */
.german-flag-gradient {
  background: linear-gradient(to right, #000000 33%, #FF0000 33%, #FF0000 67%, #FFCC00 67%);
}

/* Patterns and textures */
.dots-pattern {
  background-image: radial-gradient(hsl(var(--neutral-200)) 1px, transparent 1px);
  background-size: 20px 20px;
}