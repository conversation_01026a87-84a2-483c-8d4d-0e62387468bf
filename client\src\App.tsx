import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ui/theme-provider";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/Dashboard";
import Reading from "@/pages/Reading";
import Writing from "@/pages/Writing";
import Listening from "@/pages/Listening";
import Speaking from "@/pages/Speaking";
import MockTests from "@/pages/MockTests";
import TestHistory from "@/pages/TestHistory";
import StudyMaterials from "@/pages/StudyMaterials";
import Settings from "@/pages/Settings";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Sidebar } from "@/components/layout/Sidebar";
import { MobileNav } from "@/components/layout/MobileNav";
import ExerciseDetail from "./pages/ExerciseDetail";

function Router() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-6">
        <div className="flex-grow pb-20 md:pb-0">
          <Switch>
            <Route path="/" component={Dashboard} />
            <Route path="/reading" component={Reading} />
            <Route path="/writing" component={Writing} />
            <Route path="/listening" component={Listening} />
            <Route path="/speaking" component={Speaking} />
            <Route path="/mock-tests" component={MockTests} />
            <Route path="/test-history" component={TestHistory} />
            <Route path="/study-materials" component={StudyMaterials} />
            <Route path="/settings" component={Settings} />
            <Route path="/:skill/:id" component={ExerciseDetail} />
            <Route component={NotFound} />
          </Switch>
        </div>
      </main>
      <MobileNav />
      <Footer />
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light">
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
