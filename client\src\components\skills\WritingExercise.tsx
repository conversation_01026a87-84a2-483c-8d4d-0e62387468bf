import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface WritingExerciseProps {
  exerciseId: number;
}

export function WritingExercise({ exerciseId }: WritingExerciseProps) {
  const { data: exercise, isLoading } = useQuery<Exercise>({
    queryKey: [`/api/exercises/${exerciseId}`],
  });

  const [answer, setAnswer] = useState("");
  const [feedback, setFeedback] = useState<string | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const { toast } = useToast();

  const submitMutation = useMutation({
    mutationFn: async (data: { exerciseId: number; answer: string }) => {
      const response = await apiRequest("POST", "/api/activities", {
        userId: 1, // In a real app, this would come from auth context
        type: "exercise",
        exerciseId: data.exerciseId,
        completed: new Date(),
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Writing submitted",
        description: "Your answer has been submitted for review.",
      });
      // Simulate feedback
      setTimeout(() => {
        setFeedback(generateFeedback(answer));
      }, 1000);
    },
    onError: () => {
      toast({
        title: "Submission failed",
        description: "There was an error submitting your answer. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    setAnswer(text);
    setWordCount(text.trim() === "" ? 0 : text.trim().split(/\s+/).length);
  };

  const handleSubmit = () => {
    if (answer.trim() === "") {
      toast({
        title: "Empty submission",
        description: "Please write your answer before submitting.",
        variant: "destructive",
      });
      return;
    }

    const minWords = exercise?.content.minWords || 50;
    if (wordCount < minWords) {
      toast({
        title: "Too short",
        description: `Your answer should be at least ${minWords} words. Current: ${wordCount} words.`,
        variant: "destructive",
      });
      return;
    }

    submitMutation.mutate({ exerciseId, answer });
  };

  const generateFeedback = (text: string) => {
    // This is a simplified feedback generator
    // In a real app, this would come from the backend with NLP analysis
    
    const feedback = {
      grammar: "Your grammar is generally good, but watch out for verb conjugations in the past tense.",
      vocabulary: "You've used a good range of vocabulary. Consider using more connectors like 'außerdem' and 'trotzdem'.",
      structure: "Your text has a clear beginning and end, but the middle part could use more organization.",
      overall: "Overall, this is a solid piece of writing at A2/B1 level. Keep practicing!"
    };
    
    return Object.entries(feedback).map(([key, value]) => `${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`).join("\n\n");
  };

  if (isLoading) {
    return (
      <div className="mt-8 border border-neutral-medium rounded-lg p-6">
        <Skeleton className="h-7 w-72 mb-4" />
        <Skeleton className="h-24 w-full mb-4" />
        <Skeleton className="h-40 w-full mb-4" />
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-9 w-32" />
        </div>
      </div>
    );
  }

  if (!exercise) {
    return <div>Exercise not found</div>;
  }

  const { prompt, minWords, maxWords } = exercise.content as any;

  return (
    <div className="mt-8 border border-neutral-medium rounded-lg p-6">
      <h3 className="font-bold text-neutral-darker mb-4">Writing Exercise: {exercise.title}</h3>
      
      <div className="bg-neutral-light p-4 rounded-lg mb-4">
        <h4 className="font-medium text-neutral-darker mb-2">Instructions</h4>
        <p className="text-neutral-darker leading-relaxed">
          {prompt}
        </p>
        <p className="text-sm text-neutral-dark mt-3">
          Write between {minWords} and {maxWords} words.
        </p>
      </div>
      
      <div className="mb-4">
        <Textarea 
          placeholder="Write your answer in German here..."
          className="min-h-[200px] resize-y"
          value={answer}
          onChange={handleTextChange}
          disabled={feedback !== null}
        />
        <div className="flex justify-between items-center mt-2 text-sm text-neutral-dark">
          <span>Word count: {wordCount}</span>
          <span>{feedback ? "Submitted" : `${minWords}-${maxWords} words`}</span>
        </div>
      </div>
      
      {feedback && (
        <div className="bg-blue-50 p-4 rounded-lg mb-4">
          <h4 className="font-medium text-neutral-darker mb-2">Feedback</h4>
          <p className="text-neutral-darker whitespace-pre-line">
            {feedback}
          </p>
        </div>
      )}
      
      <div className="flex justify-end">
        {feedback ? (
          <Button onClick={() => window.location.reload()}>Try Another</Button>
        ) : (
          <Button 
            onClick={handleSubmit}
            disabled={submitMutation.isPending}
          >
            {submitMutation.isPending ? "Submitting..." : "Submit"}
          </Button>
        )}
      </div>
    </div>
  );
}
