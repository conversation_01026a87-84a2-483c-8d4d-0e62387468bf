import { Link, useLocation } from "wouter";

export function Sidebar() {
  const [location] = useLocation();

  return (
    <aside className="hidden md:block w-64 bg-white rounded-lg shadow-md p-4 h-fit sticky top-6">
      <nav>
        <ul className="space-y-2">
          <li>
            <Link 
              href="/"
              className={`flex items-center p-3 rounded-md ${
                location === "/" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">dashboard</span>
              Dashboard
            </Link>
          </li>
          <li className="pt-2 pb-1">
            <p className="text-neutral-dark text-sm font-medium px-3">SKILLS</p>
          </li>
          <li>
            <Link 
              href="/reading"
              className={`flex items-center p-3 rounded-md ${
                location === "/reading" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">menu_book</span>
              Reading
            </Link>
          </li>
          <li>
            <Link 
              href="/writing"
              className={`flex items-center p-3 rounded-md ${
                location === "/writing" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">edit</span>
              Writing
            </Link>
          </li>
          <li>
            <Link 
              href="/listening"
              className={`flex items-center p-3 rounded-md ${
                location === "/listening" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">headphones</span>
              Listening
            </Link>
          </li>
          <li>
            <Link 
              href="/speaking"
              className={`flex items-center p-3 rounded-md ${
                location === "/speaking" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">record_voice_over</span>
              Speaking
            </Link>
          </li>
          <li className="pt-2 pb-1">
            <p className="text-neutral-dark text-sm font-medium px-3">TESTS</p>
          </li>
          <li>
            <Link 
              href="/mock-tests"
              className={`flex items-center p-3 rounded-md ${
                location === "/mock-tests" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">assignment</span>
              Mock Tests
            </Link>
          </li>
          <li>
            <Link 
              href="/test-history"
              className={`flex items-center p-3 rounded-md ${
                location === "/test-history" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">history</span>
              Test History
            </Link>
          </li>
          <li className="pt-2 pb-1">
            <p className="text-neutral-dark text-sm font-medium px-3">RESOURCES</p>
          </li>
          <li>
            <Link 
              href="/study-materials"
              className={`flex items-center p-3 rounded-md ${
                location === "/study-materials" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">folder</span>
              Study Materials
            </Link>
          </li>
          <li>
            <Link 
              href="/settings"
              className={`flex items-center p-3 rounded-md ${
                location === "/settings" 
                  ? "text-primary bg-blue-50 font-medium" 
                  : "hover:bg-neutral-light text-neutral-darker"
              }`}
            >
              <span className="material-icons mr-3">settings</span>
              Settings
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  );
}
