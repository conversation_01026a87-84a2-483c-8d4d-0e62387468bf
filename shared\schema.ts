import { pgTable, text, serial, integer, boolean, timestamp, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  level: text("level").default("A2/B1").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  firstName: true,
  lastName: true,
  level: true,
});

// Skill type enum
export const skillType = z.enum(["reading", "writing", "listening", "speaking"]);
export type SkillType = z.infer<typeof skillType>;

// Skills tracking
export const skills = pgTable("skills", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  type: text("type").notNull(), // reading, writing, listening, speaking
  proficiency: integer("proficiency").notNull(), // percentage 0-100
  lastActivity: timestamp("last_activity").notNull(),
});

export const insertSkillSchema = createInsertSchema(skills).pick({
  userId: true,
  type: true,
  proficiency: true,
  lastActivity: true,
});

// Study streak
export const streaks = pgTable("streaks", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  date: timestamp("date").notNull(),
  duration: integer("duration").notNull(), // in minutes
});

export const insertStreakSchema = createInsertSchema(streaks).pick({
  userId: true,
  date: true,
  duration: true,
});

// Exercise types
export const exercises = pgTable("exercises", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  skill: text("skill").notNull(), // reading, writing, listening, speaking
  level: text("level").notNull(), // A1, A2, B1, B2
  content: json("content").notNull(), // Questions, text, audio URL, etc.
});

export const insertExerciseSchema = createInsertSchema(exercises).pick({
  title: true,
  description: true,
  skill: true,
  level: true,
  content: true,
});

// User activities (completed exercises, tests, etc.)
export const activities = pgTable("activities", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  type: text("type").notNull(), // exercise, test, study
  exerciseId: integer("exercise_id"),
  score: integer("score"), // percentage
  completed: timestamp("completed").notNull(),
});

export const insertActivitySchema = createInsertSchema(activities).pick({
  userId: true,
  type: true,
  exerciseId: true,
  score: true,
  completed: true,
});

// Mock tests
export const mockTests = pgTable("mock_tests", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  level: text("level").notNull(), // A1, A2, B1, B2
  duration: integer("duration").notNull(), // in minutes
  description: text("description").notNull(),
  sections: json("sections").notNull(), // Array of sections with durations
});

export const insertMockTestSchema = createInsertSchema(mockTests).pick({
  title: true,
  level: true,
  duration: true,
  description: true,
  sections: true,
});

// Test attempts
export const testAttempts = pgTable("test_attempts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  testId: integer("test_id").notNull(),
  score: integer("score").notNull(), // percentage
  passed: boolean("passed").notNull(),
  completed: timestamp("completed").notNull(),
  sectionScores: json("section_scores").notNull(), // Scores for each section
});

export const insertTestAttemptSchema = createInsertSchema(testAttempts).pick({
  userId: true,
  testId: true,
  score: true,
  passed: true,
  completed: true,
  sectionScores: true,
});

// Speaking topics for avatar conversation
export const speakingTopics = pgTable("speaking_topics", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  level: text("level").notNull(), // Beginner, Intermediate, Advanced
  prompts: json("prompts").notNull(), // Array of conversation prompts
});

export const insertSpeakingTopicSchema = createInsertSchema(speakingTopics).pick({
  title: true,
  description: true,
  level: true,
  prompts: true,
});

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Skill = typeof skills.$inferSelect;
export type InsertSkill = z.infer<typeof insertSkillSchema>;

export type Streak = typeof streaks.$inferSelect;
export type InsertStreak = z.infer<typeof insertStreakSchema>;

export type Exercise = typeof exercises.$inferSelect;
export type InsertExercise = z.infer<typeof insertExerciseSchema>;

export type Activity = typeof activities.$inferSelect;
export type InsertActivity = z.infer<typeof insertActivitySchema>;

export type MockTest = typeof mockTests.$inferSelect;
export type InsertMockTest = z.infer<typeof insertMockTestSchema>;

export type TestAttempt = typeof testAttempts.$inferSelect;
export type InsertTestAttempt = z.infer<typeof insertTestAttemptSchema>;

export type SpeakingTopic = typeof speakingTopics.$inferSelect;
export type InsertSpeakingTopic = z.infer<typeof insertSpeakingTopicSchema>;
