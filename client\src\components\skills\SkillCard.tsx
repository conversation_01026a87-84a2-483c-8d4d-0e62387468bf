import { ReactNode } from "react";

interface SkillCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  proficiency: number;
  lastActivity: string;
  children?: ReactNode;
}

export function SkillCard({ 
  title, 
  description, 
  icon, 
  proficiency, 
  lastActivity, 
  children 
}: SkillCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-neutral-darker flex items-center">
            {icon}
            {title}
          </h2>
          <p className="text-neutral-dark mt-1">{description}</p>
        </div>
        <div className="mt-4 md:mt-0">
          <select className="bg-neutral-light border border-neutral-medium rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary">
            <option>Level: A2/B1</option>
            <option>Level: A1/A2</option>
            <option>Level: B1/B2</option>
          </select>
        </div>
      </div>
      
      <div className="flex items-center justify-between p-3 bg-neutral-light rounded-lg mb-6">
        <div className="flex items-center">
          <div className="mr-4">
            <p className="text-sm text-neutral-dark mb-1">Proficiency</p>
            <p className="text-lg font-bold text-primary">{proficiency}%</p>
          </div>
          <div className="w-40 bg-neutral-medium rounded-full h-2">
            <div 
              className="bg-primary rounded-full h-2 progress-bar" 
              style={{ width: `${proficiency}%` }}
            ></div>
          </div>
        </div>
        <div>
          <p className="text-sm text-neutral-dark">Last activity</p>
          <p className="text-sm font-medium">{lastActivity}</p>
        </div>
      </div>
      
      {children}
    </div>
  );
}
