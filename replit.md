# DeutschPrep: German Language Learning Application

## Overview

DeutschPrep is a web application designed to help users prepare for the A2/B1 German language exam. It provides comprehensive skills training across reading, writing, listening, and speaking, with progress tracking, mock tests, and personalized study recommendations.

The application follows a modern React frontend with an Express backend architecture, using PostgreSQL (via Drizzle ORM) for data storage. It features a component-based UI built with shadcn/ui components, a responsive design for both desktop and mobile, and a RESTful API for data communication.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture

- **Framework**: React with TypeScript for type safety
- **Routing**: Uses Wouter for lightweight client-side routing
- **State Management**: Combination of React Query for server state and React hooks for local state
- **UI Components**: Shadcn/UI component library based on Radix UI primitives
- **Styling**: Tailwind CSS with a custom theme configuration
- **Build Tool**: Vite for fast development and optimized production builds

The frontend is organized into pages, components, hooks, and utility functions. Component reuse is emphasized with UI components separated from business logic.

### Backend Architecture

- **Framework**: Express.js with TypeScript
- **API**: RESTful endpoints for data operations
- **Database Access**: Drizzle ORM for type-safe database operations
- **Authentication**: Session-based (through connect-pg-simple)
- **Middleware**: JSON parsing, error handling, and logging

The backend handles API requests, database operations, and serves the frontend in production.

### Data Storage

- **Database**: PostgreSQL (configured for Neon Serverless Postgres)
- **ORM**: Drizzle ORM with Zod for schema validation
- **Schema**: Structured around users, skills, exercises, and test attempts

The data model focuses on user progress tracking across different language skills.

## Key Components

### Frontend Components

1. **Layout Components**:
   - Header, Footer, Sidebar, and MobileNav for navigation
   - Responsive design with different layouts for desktop and mobile

2. **Page Components**:
   - Dashboard: Overview of user progress and suggested activities
   - Skill Pages (Reading, Writing, Listening, Speaking): Skill-specific exercises
   - MockTests: Exam simulation and practice
   - TestHistory: Record of past test attempts
   - StudyMaterials: Reference materials for learning
   - Settings: User preferences

3. **UI Components**:
   - Shadcn/UI component library (buttons, cards, dialogs, etc.)
   - Custom components for specific features (ProgressOverview, StudyStreak, etc.)

4. **Feature Components**:
   - Exercise components for different skill types
   - Progress tracking visualizations
   - Mock test interface

### Backend Components

1. **API Routes**:
   - User management
   - Skills tracking
   - Exercise management
   - Test attempts and results

2. **Storage Interface**:
   - Database abstraction layer
   - CRUD operations for all entities

3. **Server Configuration**:
   - Vite middleware for development
   - Static file serving for production

## Data Flow

1. **User Authentication**:
   - User logs in via the auth system
   - Session is established and maintained with cookies

2. **Data Fetching**:
   - React Query manages API requests to the backend
   - Components request data through custom hooks
   - Backend processes requests, performs database operations, and returns results

3. **User Interactions**:
   - User interacts with UI components
   - Frontend dispatches API requests to create/update data
   - Backend validates input with Zod schemas
   - Database is updated through Drizzle ORM
   - Updated data is reflected in the UI

4. **Progress Tracking**:
   - User completes exercises and tests
   - Results are submitted to the backend
   - Skills and proficiency levels are updated
   - Dashboard displays updated progress information

## External Dependencies

### Frontend Dependencies

- **@radix-ui/react-***: UI component primitives
- **@tanstack/react-query**: Data fetching and caching
- **class-variance-authority & clsx**: Utility for managing component styles
- **date-fns**: Date manipulation utilities
- **tailwindcss**: Utility-first CSS framework
- **wouter**: Lightweight routing library

### Backend Dependencies

- **express**: Web server framework
- **drizzle-orm**: Database ORM
- **drizzle-zod**: Schema validation
- **@neondatabase/serverless**: PostgreSQL client for serverless environments
- **connect-pg-simple**: Session management

## Deployment Strategy

The application is configured for deployment on Replit with the following setup:

1. **Development Mode**:
   - `npm run dev` runs the Express server with Vite in middleware mode
   - Hot module replacement for frontend changes
   - API routes are accessible for testing

2. **Production Build**:
   - `npm run build` creates optimized frontend assets with Vite
   - Backend is bundled with esbuild
   - Static assets are served by Express

3. **Database**:
   - PostgreSQL 16 module is included in the Replit configuration
   - Database schema can be updated with `npm run db:push`

4. **Autoscaling**:
   - Deployment is configured for autoscaling in Replit
   - Port 5000 is mapped to port 80 for external access

The application is designed to be deployed as a single unit, with the backend serving both the API and the frontend assets.