import { SpeakingTopic } from "@shared/schema";
import { <PERSON> } from "wouter";

interface SpeakingTopicCardProps {
  topic: SpeakingTopic;
}

export function SpeakingTopicCard({ topic }: SpeakingTopicCardProps) {
  let levelClass = "text-primary";
  
  if (topic.level === "Intermediate") {
    levelClass = "text-warning";
  } else if (topic.level === "Advanced") {
    levelClass = "text-error";
  }

  return (
    <div className="border border-neutral-medium rounded-lg p-4 hover:shadow-md transition cursor-pointer">
      <h4 className="font-medium text-neutral-darker mb-2">{topic.title}</h4>
      <p className="text-sm text-neutral-dark mb-3">{topic.description}</p>
      <div className="flex justify-between items-center">
        <span className="text-xs bg-blue-100 text-primary px-2 py-1 rounded-full">
          {topic.level}
        </span>
        <Link href={`/speaking/${topic.id}`}>
          <button className="text-primary hover:text-opacity-80">
            <span className="material-icons">arrow_forward</span>
          </button>
        </Link>
      </div>
    </div>
  );
}
