import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useSpeak } from "@/hooks/use-speak";
import { SpeakingTopic } from "@shared/schema";

interface AvatarChatProps {
  topic: SpeakingTopic;
}

export function AvatarChat({ topic }: AvatarChatProps) {
  const { speak, startListening, stopListening, isSpeaking, isListening, transcript } = useSpeak();
  const [messages, setMessages] = useState<Array<{ text: string; isUser: boolean }>>([]);
  const [userInput, setUserInput] = useState("");
  const [currentPrompt, setCurrentPrompt] = useState<number>(0);
  const [feedback, setFeedback] = useState<string | null>(null);
  
  useEffect(() => {
    if (messages.length === 0) {
      const prompts = topic.prompts as string[];
      if (prompts && prompts.length > 0) {
        const initialMessage = { text: prompts[0], isUser: false };
        setMessages([initialMessage]);
        speak(prompts[0]);
      }
    }
  }, [topic, messages.length, speak]);

  useEffect(() => {
    if (transcript) {
      setUserInput(transcript);
    }
  }, [transcript]);

  const handleSendMessage = () => {
    if (userInput.trim() === "") return;
    
    // Add user message
    const newUserMessage = { text: userInput, isUser: true };
    setMessages(prevMessages => [...prevMessages, newUserMessage]);
    
    // Generate bot response
    setTimeout(() => {
      const prompts = topic.prompts as string[];
      
      // Generate feedback based on user input
      // In a real app, this would use NLP to analyze the input
      if (Math.random() > 0.5) {
        setFeedback("Good pronunciation! Your sentence structure was correct, but remember to use the formal \"Sie\" form consistently.");
      } else {
        setFeedback("Your vocabulary is good, but pay attention to your word order. In German, the verb should be in the second position in a statement.");
      }
      
      // Move to next prompt if available
      if (currentPrompt < prompts.length - 1) {
        const nextPrompt = prompts[currentPrompt + 1];
        const botResponse = { text: nextPrompt, isUser: false };
        setMessages(prevMessages => [...prevMessages, botResponse]);
        setCurrentPrompt(currentPrompt + 1);
        speak(nextPrompt);
      } else {
        // End of exercise
        const finalResponse = { 
          text: "Vielen Dank für das Gespräch! Du hast die Übung abgeschlossen.", 
          isUser: false 
        };
        setMessages(prevMessages => [...prevMessages, finalResponse]);
        speak(finalResponse.text);
      }
    }, 1000);
    
    setUserInput("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleMicClick = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  return (
    <div className="border border-neutral-medium rounded-lg overflow-hidden">
      {/* Avatar Header */}
      <div className="bg-neutral-light p-4 border-b border-neutral-medium flex items-center justify-between">
        <div className="flex items-center">
          <img 
            src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" 
            alt="German tutor avatar" 
            className="w-10 h-10 rounded-full object-cover"
          />
          <div className="ml-3">
            <h3 className="font-medium text-neutral-darker">Frau Schmidt</h3>
            <p className="text-xs text-neutral-dark">German Conversation Partner</p>
          </div>
        </div>
        <div>
          <select className="bg-white border border-neutral-medium rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
            <option>Topic: {topic.title}</option>
            <option disabled>Other topics coming soon</option>
          </select>
        </div>
      </div>
      
      {/* Chat Area */}
      <div className="h-96 overflow-y-auto p-4 bg-neutral-light bg-opacity-50">
        {messages.map((message, index) => (
          <div key={index} className={`flex mb-4 ${message.isUser ? "justify-end" : ""}`}>
            {!message.isUser && (
              <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0 mr-3">
                <img 
                  src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" 
                  alt="Avatar" 
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className={`${
              message.isUser 
                ? "bg-primary bg-opacity-10 mr-3" 
                : "bg-white ml-3"
              } rounded-lg py-2 px-4 max-w-[80%]`}
            >
              <p className="text-neutral-darker">{message.text}</p>
            </div>
            {message.isUser && (
              <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0 bg-neutral-light flex items-center justify-center">
                <span className="material-icons text-neutral-dark">person</span>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {/* Feedback Area */}
      {feedback && (
        <div className="border-t border-neutral-medium p-4 bg-success bg-opacity-10">
          <div className="flex items-start mb-2">
            <span className="material-icons text-success mr-2 mt-0.5">check_circle</span>
            <div>
              <h4 className="font-medium text-neutral-darker">Feedback</h4>
              <p className="text-sm text-neutral-dark">{feedback}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Input Area */}
      <div className="border-t border-neutral-medium p-4 flex items-center">
        <Button
          variant="outline"
          size="icon"
          className={`rounded-full mr-3 ${
            isListening ? "bg-primary text-white" : "bg-neutral-light text-neutral-darker"
          }`}
          onClick={handleMicClick}
        >
          <span className="material-icons">mic</span>
        </Button>
        <Input
          placeholder="Type in German..."
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          onKeyDown={handleKeyPress}
          className="flex-grow"
        />
        <Button
          className="ml-3 rounded-full w-10 h-10 flex items-center justify-center"
          onClick={handleSendMessage}
          disabled={userInput.trim() === ""}
        >
          <span className="material-icons">send</span>
        </Button>
      </div>
    </div>
  );
}
