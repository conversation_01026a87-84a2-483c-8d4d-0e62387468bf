import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON> } from "wouter";
import { Skeleton } from "@/components/ui/skeleton";

export default function Dashboard() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="p-4">
        <Skeleton className="h-36 w-full rounded-2xl mb-6" />
        <Skeleton className="h-8 w-48 mb-4" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-40 w-full rounded-2xl" />
          ))}
        </div>
        <Skeleton className="h-8 w-48 mb-4" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {[...Array(2)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full rounded-2xl" />
          ))}
        </div>
      </div>
    );
  }

  if (!user) {
    return <div>Please log in to view your dashboard</div>;
  }

  // Skill data (this would come from an API in a real app)
  const skills = [
    { 
      id: 1, 
      name: "Reading", 
      icon: "menu_book", 
      color: "bg-reading",
      progress: 78, 
      message: "Great progress! Focus on complex texts.",
      action: "Practice reading",
      link: "/reading",
      disabled: true
    },
    { 
      id: 2, 
      name: "Writing", 
      icon: "edit", 
      color: "bg-writing",
      progress: 65, 
      message: "Keep practicing formal letters!",
      action: "Practice writing",
      link: "/writing",
      disabled: true
    },
    { 
      id: 3, 
      name: "Listening", 
      icon: "headphones", 
      color: "bg-listening",
      progress: 82, 
      message: "Excellent! Your best skill so far.",
      action: "Practice listening",
      link: "/listening",
      disabled: false
    },
    { 
      id: 4, 
      name: "Speaking", 
      icon: "record_voice_over", 
      color: "bg-speaking",
      progress: 71, 
      message: "Practice with AI tutor available!",
      action: "Practice with AI",
      link: "/speaking",
      disabled: false
    }
  ];

  return (
    <div className="px-4 py-2 max-w-7xl mx-auto">
      {/* Welcome Banner */}
      <div className="bg-primary-gradient rounded-3xl px-8 py-6 mb-8 text-white flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-1">Guten Tag, {user.firstName}!</h1>
          <p className="text-white/90 mb-4">Ready to master your German {user.level} exam? Let's continue your journey!</p>
          
          <div className="flex items-center space-x-6">
            <div className="flex items-center">
              <span className="material-icons mr-2">local_fire_department</span>
              <span className="font-medium">7 day streak</span>
            </div>
            <div className="flex items-center">
              <span className="material-icons mr-2">emoji_events</span>
              <span className="font-medium">1240 points</span>
            </div>
          </div>
        </div>
        <div className="float-animation hidden md:block">
          <div className="h-24 w-24 bg-white/20 rounded-full flex items-center justify-center">
            <span className="material-icons text-5xl">smart_toy</span>
          </div>
        </div>
      </div>

      {/* Skills Progress */}
      <h2 className="text-xl font-bold mb-4">Your Skills Progress</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {skills.map(skill => (
          <div key={skill.id} className="skill-card p-5">
            <div className="flex justify-between items-start mb-2">
              <div className="flex items-center">
                <div className={`${skill.color} w-10 h-10 rounded-lg flex items-center justify-center text-white mr-3`}>
                  <span className="material-icons">{skill.icon}</span>
                </div>
                <div>
                  <h3 className="font-medium">{skill.name}</h3>
                </div>
              </div>
              <div className="text-xl font-bold">{skill.progress}%</div>
            </div>
            
            <div className="bg-neutral-200 h-2 rounded-full mb-3 mt-2">
              <div 
                className={`${skill.color} h-2 rounded-full`}
                style={{ width: `${skill.progress}%` }}
              ></div>
            </div>
            
            <div className="flex justify-between items-center">
              <p className="text-sm text-neutral-600">{skill.message}</p>
              {skill.disabled ? (
                <span className="text-neutral-400 text-sm font-medium">Coming soon</span>
              ) : (
                <Link href={skill.link}>
                  <button className="text-primary hover:underline text-sm font-medium">{skill.action}</button>
                </Link>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Start */}
      <h2 className="text-xl font-bold mb-4">Quick Start</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-neutral-300 quick-start-card p-6 text-white opacity-70">
          <div className="flex justify-between mb-4">
            <h3 className="font-bold text-xl">Mock Test</h3>
            <span className="material-icons">timer</span>
          </div>
          <p className="mb-4">Take a full A2/B1 practice exam</p>
          <div className="flex items-center text-sm space-x-4 mb-4">
            <div className="flex items-center">
              <span className="material-icons text-sm mr-1">schedule</span>
              <span>90 minutes</span>
            </div>
            <div className="flex items-center">
              <span className="material-icons text-sm mr-1">check_circle</span>
              <span>All skills</span>
            </div>
          </div>
          <button disabled className="bg-white text-neutral-500 rounded-full px-4 py-2 font-medium w-full cursor-not-allowed">
            Coming soon
          </button>
        </div>
        
        <div style={{backgroundColor: "#2AC769"}} className="quick-start-card p-6 text-white shadow-lg border-4 border-success">
          <div className="flex justify-between mb-4">
            <h3 className="font-bold text-xl">AI Conversation</h3>
            <span className="material-icons">smart_toy</span>
          </div>
          <p className="mb-4">Practice speaking with our AI tutor</p>
          <div className="flex items-center text-sm space-x-4 mb-4">
            <div className="flex items-center">
              <span className="material-icons text-sm mr-1">record_voice_over</span>
              <span>Voice interaction</span>
            </div>
            <div className="flex items-center">
              <span className="material-icons text-sm mr-1">psychology</span>
              <span>Real feedback</span>
            </div>
          </div>
          <Link href="/speaking">
            <button className="bg-white text-success rounded-full px-4 py-2 font-medium w-full">
              Start Conversation
            </button>
          </Link>
        </div>
      </div>

      {/* AI Discussion */}
      <div className="skill-card p-6 mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-xl">Discuss your progress with AI agent</h3>
          <span className="bg-success text-white text-xs px-3 py-1 rounded-full">Active</span>
        </div>
        
        <div className="flex flex-col md:flex-row items-center">
          <div className="ai-avatar mb-4 md:mb-0 md:mr-6 float-animation">
            <span className="material-icons text-4xl">smart_toy</span>
          </div>
          <div>
            <h4 className="font-medium mb-2">Meet Hans, your AI tutor</h4>
            <p className="text-neutral-600 mb-4">"Wie geht es mit Ihrem Deutschlernen voran? Erzählen Sie mir von Ihren Fortschritten."</p>
            <Link href="/speaking">
              <button className="bg-success text-white rounded-full px-4 py-2 font-medium">
                Continue Conversation
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
