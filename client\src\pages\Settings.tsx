import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Skeleton } from "@/components/ui/skeleton";
import { useTheme } from "@/components/ui/theme-provider";

export default function Settings() {
  const { user, isLoading } = useAuth();
  const { theme, setTheme } = useTheme();

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <Skeleton className="h-8 w-48 mb-2" />
        <Skeleton className="h-5 w-64 mb-6" />
        <Skeleton className="h-64 w-full mb-6" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!user) {
    return <div>Please log in to view your settings</div>;
  }

  return (
    <section id="settings" className="mb-8">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-neutral-darker flex items-center">
              <span className="material-icons mr-2 text-primary">settings</span>
              Settings
            </h2>
            <p className="text-neutral-dark mt-1">Manage your account and application preferences</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 gap-6">
          {/* Profile Settings */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Profile Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input id="firstName" defaultValue={user.firstName} />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input id="lastName" defaultValue={user.lastName} />
                </div>
                <div>
                  <Label htmlFor="username">Username</Label>
                  <Input id="username" defaultValue={user.username} />
                </div>
                <div>
                  <Label htmlFor="examLevel">Exam Level</Label>
                  <Select defaultValue={user.level}>
                    <SelectTrigger id="examLevel">
                      <SelectValue placeholder="Select exam level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A1/A2">A1/A2</SelectItem>
                      <SelectItem value="A2/B1">A2/B1</SelectItem>
                      <SelectItem value="B1/B2">B1/B2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button className="mt-4">Save Changes</Button>
            </CardContent>
          </Card>
          
          {/* App Settings */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Application Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="darkMode">Dark Mode</Label>
                    <p className="text-sm text-neutral-dark">Enable dark theme for the application</p>
                  </div>
                  <Switch 
                    id="darkMode" 
                    checked={theme === "dark"}
                    onCheckedChange={(checked) => setTheme(checked ? "dark" : "light")}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="notifications">Email Notifications</Label>
                    <p className="text-sm text-neutral-dark">Receive updates about your progress</p>
                  </div>
                  <Switch id="notifications" defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="reminders">Study Reminders</Label>
                    <p className="text-sm text-neutral-dark">Get daily reminders to practice</p>
                  </div>
                  <Switch id="reminders" defaultChecked />
                </div>
              </div>
              <Button className="mt-4">Save Preferences</Button>
            </CardContent>
          </Card>
          
          {/* Password Settings */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Change Password</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input id="currentPassword" type="password" />
                </div>
                <div>
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input id="newPassword" type="password" />
                </div>
                <div>
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input id="confirmPassword" type="password" />
                </div>
              </div>
              <Button className="mt-4">Update Password</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
