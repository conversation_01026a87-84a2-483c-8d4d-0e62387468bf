import { Link } from "wouter";
import { MockTest } from "@shared/schema";

interface MockTestCardProps {
  test: MockTest;
}

export function MockTestCard({ test }: MockTestCardProps) {
  return (
    <div className="border border-neutral-medium rounded-lg p-5 hover:shadow-md transition">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-bold text-neutral-darker text-xl">{test.title}</h3>
        <span className="bg-accent text-neutral-darker px-3 py-1 rounded-full text-sm font-bold">
          {test.duration} min
        </span>
      </div>
      <p className="text-neutral-dark mb-4">{test.description}</p>
      
      <div className="space-y-2 mb-6">
        {test.sections.map((section: any, index: number) => (
          <div key={index} className="flex justify-between text-sm">
            <span>{section.name}</span>
            <span>{section.duration} min</span>
          </div>
        ))}
      </div>
      
      <Link href={`/mock-tests/${test.id}`}>
        <button className="w-full bg-primary text-white rounded-lg py-2 hover:bg-opacity-90 transition">
          Start Test
        </button>
      </Link>
    </div>
  );
}
