import {
  users, User, InsertUser,
  skills, Skill, InsertSkill,
  streaks, Streak, InsertStreak,
  exercises, Exercise, InsertExercise,
  activities, Activity, InsertActivity,
  mockTests, MockTest, InsertMockTest,
  testAttempts, TestAttempt, InsertTestAttempt,
  speakingTopics, SpeakingTopic, InsertSpeakingTopic,
  SkillType
} from "@shared/schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Skills methods
  getUserSkills(userId: number): Promise<Skill[]>;
  updateUserSkill(userId: number, skillType: SkillType, proficiency: number): Promise<Skill>;
  
  // Streak methods
  getUserStreaks(userId: number): Promise<Streak[]>;
  addUserStreak(streak: InsertStreak): Promise<Streak>;
  
  // Exercise methods
  getExercises(): Promise<Exercise[]>;
  getExercisesBySkill(skill: SkillType, level?: string): Promise<Exercise[]>;
  getExercise(id: number): Promise<Exercise | undefined>;
  createExercise(exercise: InsertExercise): Promise<Exercise>;
  
  // Activity methods
  getUserActivities(userId: number, limit?: number): Promise<Activity[]>;
  createActivity(activity: InsertActivity): Promise<Activity>;
  
  // Mock tests methods
  getMockTests(): Promise<MockTest[]>;
  getMockTest(id: number): Promise<MockTest | undefined>;
  createMockTest(test: InsertMockTest): Promise<MockTest>;
  
  // Test attempts methods
  getUserTestAttempts(userId: number): Promise<TestAttempt[]>;
  createTestAttempt(attempt: InsertTestAttempt): Promise<TestAttempt>;
  
  // Speaking topics methods
  getSpeakingTopics(): Promise<SpeakingTopic[]>;
  getSpeakingTopic(id: number): Promise<SpeakingTopic | undefined>;
  createSpeakingTopic(topic: InsertSpeakingTopic): Promise<SpeakingTopic>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private skills: Map<number, Skill>;
  private streaks: Map<number, Streak>;
  private exercises: Map<number, Exercise>;
  private activities: Map<number, Activity>;
  private mockTests: Map<number, MockTest>;
  private testAttempts: Map<number, TestAttempt>;
  private speakingTopics: Map<number, SpeakingTopic>;
  
  private userIdCounter: number;
  private skillIdCounter: number;
  private streakIdCounter: number;
  private exerciseIdCounter: number;
  private activityIdCounter: number;
  private mockTestIdCounter: number;
  private testAttemptIdCounter: number;
  private speakingTopicIdCounter: number;
  
  constructor() {
    this.users = new Map();
    this.skills = new Map();
    this.streaks = new Map();
    this.exercises = new Map();
    this.activities = new Map();
    this.mockTests = new Map();
    this.testAttempts = new Map();
    this.speakingTopics = new Map();
    
    this.userIdCounter = 1;
    this.skillIdCounter = 1;
    this.streakIdCounter = 1;
    this.exerciseIdCounter = 1;
    this.activityIdCounter = 1;
    this.mockTestIdCounter = 1;
    this.testAttemptIdCounter = 1;
    this.speakingTopicIdCounter = 1;
    
    // Initialize with demo data
    this.initializeData();
  }
  
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }
  
  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }
  
  // Skills methods
  async getUserSkills(userId: number): Promise<Skill[]> {
    return Array.from(this.skills.values()).filter(
      (skill) => skill.userId === userId,
    );
  }
  
  async updateUserSkill(userId: number, skillType: SkillType, proficiency: number): Promise<Skill> {
    const existingSkill = Array.from(this.skills.values()).find(
      (skill) => skill.userId === userId && skill.type === skillType,
    );
    
    if (existingSkill) {
      const updatedSkill: Skill = {
        ...existingSkill,
        proficiency,
        lastActivity: new Date(),
      };
      this.skills.set(existingSkill.id, updatedSkill);
      return updatedSkill;
    }
    
    const id = this.skillIdCounter++;
    const newSkill: Skill = {
      id,
      userId,
      type: skillType,
      proficiency,
      lastActivity: new Date(),
    };
    this.skills.set(id, newSkill);
    return newSkill;
  }
  
  // Streak methods
  async getUserStreaks(userId: number): Promise<Streak[]> {
    return Array.from(this.streaks.values()).filter(
      (streak) => streak.userId === userId,
    );
  }
  
  async addUserStreak(insertStreak: InsertStreak): Promise<Streak> {
    const id = this.streakIdCounter++;
    const streak: Streak = { ...insertStreak, id };
    this.streaks.set(id, streak);
    return streak;
  }
  
  // Exercise methods
  async getExercises(): Promise<Exercise[]> {
    return Array.from(this.exercises.values());
  }
  
  async getExercisesBySkill(skill: SkillType, level?: string): Promise<Exercise[]> {
    let exercises = Array.from(this.exercises.values()).filter(
      (exercise) => exercise.skill === skill,
    );
    
    if (level) {
      exercises = exercises.filter((exercise) => exercise.level === level);
    }
    
    return exercises;
  }
  
  async getExercise(id: number): Promise<Exercise | undefined> {
    return this.exercises.get(id);
  }
  
  async createExercise(insertExercise: InsertExercise): Promise<Exercise> {
    const id = this.exerciseIdCounter++;
    const exercise: Exercise = { ...insertExercise, id };
    this.exercises.set(id, exercise);
    return exercise;
  }
  
  // Activity methods
  async getUserActivities(userId: number, limit?: number): Promise<Activity[]> {
    let activities = Array.from(this.activities.values())
      .filter((activity) => activity.userId === userId)
      .sort((a, b) => b.completed.getTime() - a.completed.getTime());
    
    if (limit) {
      activities = activities.slice(0, limit);
    }
    
    return activities;
  }
  
  async createActivity(insertActivity: InsertActivity): Promise<Activity> {
    const id = this.activityIdCounter++;
    const activity: Activity = { ...insertActivity, id };
    this.activities.set(id, activity);
    return activity;
  }
  
  // Mock tests methods
  async getMockTests(): Promise<MockTest[]> {
    return Array.from(this.mockTests.values());
  }
  
  async getMockTest(id: number): Promise<MockTest | undefined> {
    return this.mockTests.get(id);
  }
  
  async createMockTest(insertTest: InsertMockTest): Promise<MockTest> {
    const id = this.mockTestIdCounter++;
    const test: MockTest = { ...insertTest, id };
    this.mockTests.set(id, test);
    return test;
  }
  
  // Test attempts methods
  async getUserTestAttempts(userId: number): Promise<TestAttempt[]> {
    return Array.from(this.testAttempts.values())
      .filter((attempt) => attempt.userId === userId)
      .sort((a, b) => b.completed.getTime() - a.completed.getTime());
  }
  
  async createTestAttempt(insertAttempt: InsertTestAttempt): Promise<TestAttempt> {
    const id = this.testAttemptIdCounter++;
    const attempt: TestAttempt = { ...insertAttempt, id };
    this.testAttempts.set(id, attempt);
    return attempt;
  }
  
  // Speaking topics methods
  async getSpeakingTopics(): Promise<SpeakingTopic[]> {
    return Array.from(this.speakingTopics.values());
  }
  
  async getSpeakingTopic(id: number): Promise<SpeakingTopic | undefined> {
    return this.speakingTopics.get(id);
  }
  
  async createSpeakingTopic(insertTopic: InsertSpeakingTopic): Promise<SpeakingTopic> {
    const id = this.speakingTopicIdCounter++;
    const topic: SpeakingTopic = { ...insertTopic, id };
    this.speakingTopics.set(id, topic);
    return topic;
  }
  
  // Initialize demo data
  private initializeData() {
    // Create a demo user
    const user: User = {
      id: this.userIdCounter++,
      username: "maria.schmidt",
      password: "password",
      firstName: "Maria",
      lastName: "Schmidt",
      level: "A2/B1",
    };
    this.users.set(user.id, user);
    
    // Create skills for the user
    const skillTypes: SkillType[] = ["reading", "writing", "listening", "speaking"];
    const proficiencies = [72, 58, 85, 47];
    const dayOffsets = [2, 1, 0, 4];
    
    skillTypes.forEach((type, index) => {
      const lastActivity = new Date();
      lastActivity.setDate(lastActivity.getDate() - dayOffsets[index]);
      
      const skill: Skill = {
        id: this.skillIdCounter++,
        userId: user.id,
        type: type,
        proficiency: proficiencies[index],
        lastActivity,
      };
      this.skills.set(skill.id, skill);
    });
    
    // Create streaks for the user
    const today = new Date();
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(today.getDate() - (6 - i));
      
      const durations = [20, 35, 15, 40, 25, 50, 0]; // Minutes per day
      
      const streak: Streak = {
        id: this.streakIdCounter++,
        userId: user.id,
        date,
        duration: durations[i],
      };
      this.streaks.set(streak.id, streak);
    }
    
    // Create exercises
    const readingExercises = [
      {
        title: "Vocabulary Building",
        description: "Expand your German vocabulary with context-based exercises",
        skill: "reading" as SkillType,
        level: "A2",
        content: {
          exercises: 15,
          imageUrl: "https://pixabay.com/get/g62ffd08abd95fbb14b8d0f2fb80e7fcdfc0cd07ecb7d721d3faa20c5e3b7f1f3d4aaa3dd06f0185501cf83a93029fb1549eab689849c960dc1a9ae80992ba705_1280.jpg"
        }
      },
      {
        title: "Practical Texts",
        description: "Practice with advertisements, forms, and everyday documents",
        skill: "reading" as SkillType,
        level: "A2",
        content: {
          exercises: 8,
          imageUrl: "https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300"
        }
      },
      {
        title: "Reading Comprehension",
        description: "Test your understanding with full texts and questions",
        skill: "reading" as SkillType,
        level: "B1",
        content: {
          exercises: 12,
          imageUrl: "https://images.unsplash.com/photo-1512820790803-83ca734da794?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300"
        }
      }
    ];
    
    readingExercises.forEach(exercise => {
      const id = this.exerciseIdCounter++;
      this.exercises.set(id, { ...exercise, id });
    });
    
    // Create mock tests
    const mockTests: InsertMockTest[] = [
      {
        title: "A2 Complete Test",
        level: "A2",
        duration: 90,
        description: "Complete mock exam including all four skills at A2 level.",
        sections: [
          { name: "Reading", duration: 25 },
          { name: "Writing", duration: 20 },
          { name: "Listening", duration: 25 },
          { name: "Speaking", duration: 20 }
        ]
      },
      {
        title: "B1 Complete Test",
        level: "B1",
        duration: 120,
        description: "Complete mock exam including all four skills at B1 level.",
        sections: [
          { name: "Reading", duration: 30 },
          { name: "Writing", duration: 30 },
          { name: "Listening", duration: 30 },
          { name: "Speaking", duration: 30 }
        ]
      }
    ];
    
    mockTests.forEach(test => {
      const id = this.mockTestIdCounter++;
      this.mockTests.set(id, { ...test, id });
    });
    
    // Create test attempts
    const testAttempts: InsertTestAttempt[] = [
      {
        userId: user.id,
        testId: 1,
        score: 78,
        passed: true,
        completed: new Date(2023, 4, 12), // May 12, 2023
        sectionScores: {
          reading: 80,
          writing: 75,
          listening: 85,
          speaking: 70
        }
      },
      {
        userId: user.id,
        testId: 2,
        score: 62,
        passed: false,
        completed: new Date(2023, 4, 5), // May 5, 2023
        sectionScores: {
          reading: 65,
          writing: 60,
          listening: 70,
          speaking: 55
        }
      },
      {
        userId: user.id,
        testId: 1,
        score: 85,
        passed: true,
        completed: new Date(2023, 3, 29), // April 29, 2023
        sectionScores: {
          reading: 90,
          writing: 80,
          listening: 85,
          speaking: 85
        }
      }
    ];
    
    testAttempts.forEach(attempt => {
      const id = this.testAttemptIdCounter++;
      this.testAttempts.set(id, { ...attempt, id });
    });
    
    // Create speaking topics
    const speakingTopics: InsertSpeakingTopic[] = [
      {
        title: "Personal Introduction",
        description: "Practice introducing yourself, your hobbies, and your background",
        level: "Beginner",
        prompts: [
          "Guten Tag! Wie heißen Sie?",
          "Woher kommen Sie?",
          "Was sind Ihre Hobbys?",
          "Wie alt sind Sie?",
          "Was machen Sie beruflich?"
        ]
      },
      {
        title: "Restaurant Ordering",
        description: "Practice ordering food, asking questions, and paying the bill",
        level: "Intermediate",
        prompts: [
          "Guten Abend! Haben Sie einen Tisch reserviert?",
          "Was möchten Sie bestellen?",
          "Haben Sie Fragen zur Speisekarte?",
          "Wie möchten Sie bezahlen?"
        ]
      },
      {
        title: "Job Interview",
        description: "Practice common job interview questions and professional responses",
        level: "Advanced",
        prompts: [
          "Erzählen Sie mir etwas über sich selbst.",
          "Warum haben Sie sich für diese Stelle beworben?",
          "Was sind Ihre Stärken und Schwächen?",
          "Wo sehen Sie sich in fünf Jahren?"
        ]
      }
    ];
    
    speakingTopics.forEach(topic => {
      const id = this.speakingTopicIdCounter++;
      this.speakingTopics.set(id, { ...topic, id });
    });
    
    // Create activities
    const now = new Date();
    const activities: InsertActivity[] = [
      {
        userId: user.id,
        type: "exercise",
        exerciseId: 3,
        score: 85,
        completed: new Date(now.getTime() - 2 * 60 * 60 * 1000) // 2 hours ago
      },
      {
        userId: user.id,
        type: "test",
        exerciseId: 1,
        score: 78,
        completed: new Date(now.getTime() - 24 * 60 * 60 * 1000) // 1 day ago
      },
      {
        userId: user.id,
        type: "exercise",
        exerciseId: 2,
        score: null,
        completed: new Date(now.getTime() - 26 * 60 * 60 * 1000) // 1 day + 2 hours ago
      }
    ];
    
    activities.forEach(activity => {
      const id = this.activityIdCounter++;
      this.activities.set(id, { ...activity, id });
    });
  }
}

export const storage = new MemStorage();
