import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Exercise } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

interface ReadingExerciseProps {
  exerciseId: number;
}

export function ReadingExercise({ exerciseId }: ReadingExerciseProps) {
  const { data: exercise, isLoading } = useQuery<Exercise>({
    queryKey: [`/api/exercises/${exerciseId}`],
  });

  const [selectedAnswers, setSelectedAnswers] = useState<Record<number, string>>({});
  const [showResults, setShowResults] = useState(false);
  const { toast } = useToast();

  if (isLoading) {
    return (
      <div className="mt-8 border border-neutral-medium rounded-lg p-6">
        <Skeleton className="h-7 w-72 mb-4" />
        <Skeleton className="h-40 w-full mb-4" />
        <Skeleton className="h-6 w-40 mb-2" />
        
        {[...Array(2)].map((_, i) => (
          <div key={i} className="border border-neutral-medium rounded-lg p-4 mb-4">
            <Skeleton className="h-5 w-full mb-2" />
            <div className="space-y-2">
              {[...Array(4)].map((_, j) => (
                <div key={j} className="flex items-center">
                  <Skeleton className="h-4 w-4 mr-2" />
                  <Skeleton className="h-4 w-48" />
                </div>
              ))}
            </div>
          </div>
        ))}
        
        <div className="flex justify-end mt-4">
          <Skeleton className="h-9 w-32" />
        </div>
      </div>
    );
  }

  if (!exercise) {
    return <div>Exercise not found</div>;
  }

  const { text, questions } = exercise.content as any;

  const handleAnswerSelect = (questionId: number, answer: string) => {
    setSelectedAnswers({
      ...selectedAnswers,
      [questionId]: answer
    });
  };

  const handleCheckAnswers = () => {
    // Check if all questions are answered
    const answeredCount = Object.keys(selectedAnswers).length;
    if (answeredCount < questions.length) {
      toast({
        title: "Please answer all questions",
        description: `You've answered ${answeredCount} of ${questions.length} questions.`,
        variant: "destructive"
      });
      return;
    }

    setShowResults(true);
    
    // In a real app, this would submit to the backend
    toast({
      title: "Answers checked",
      description: "Your answers have been evaluated.",
    });
  };

  const isCorrectAnswer = (questionId: number, answer: string) => {
    if (!showResults) return false;
    
    const question = questions.find((q: any) => q.id === questionId);
    return question?.correctAnswer === answer;
  };

  return (
    <div className="mt-8 border border-neutral-medium rounded-lg p-6">
      <h3 className="font-bold text-neutral-darker mb-4">Exercise: {exercise.title}</h3>
      
      <div className="bg-neutral-light p-4 rounded-lg mb-4">
        <h4 className="font-medium text-neutral-darker mb-2">{text.title}</h4>
        <p className="text-neutral-darker leading-relaxed whitespace-pre-line">
          {text.content}
        </p>
      </div>
      
      <h4 className="font-medium text-neutral-darker mb-2">Questions:</h4>
      <div className="space-y-4">
        {questions.map((question: any, index: number) => (
          <div key={question.id} className="border border-neutral-medium rounded-lg p-4">
            <p className="mb-2 text-neutral-darker">{index + 1}. {question.text}</p>
            <div className="space-y-2">
              {question.options.map((option: string) => (
                <label 
                  key={option} 
                  className={`flex items-center ${
                    showResults && (
                      isCorrectAnswer(question.id, option)
                        ? "text-success font-medium"
                        : selectedAnswers[question.id] === option && !isCorrectAnswer(question.id, option)
                          ? "text-error font-medium"
                          : ""
                    )
                  }`}
                >
                  <input 
                    type="radio" 
                    name={`q${question.id}`} 
                    className="mr-2"
                    checked={selectedAnswers[question.id] === option}
                    onChange={() => handleAnswerSelect(question.id, option)}
                    disabled={showResults}
                  />
                  <span>{option}</span>
                  {showResults && isCorrectAnswer(question.id, option) && (
                    <span className="material-icons text-success ml-2 text-sm">check_circle</span>
                  )}
                  {showResults && selectedAnswers[question.id] === option && !isCorrectAnswer(question.id, option) && (
                    <span className="material-icons text-error ml-2 text-sm">cancel</span>
                  )}
                </label>
              ))}
              {showResults && selectedAnswers[question.id] !== question.correctAnswer && (
                <p className="text-sm text-error mt-2">
                  <span className="font-medium">Correct answer:</span> {question.correctAnswer}
                </p>
              )}
              {showResults && question.explanation && (
                <p className="text-sm bg-blue-50 p-2 rounded mt-2">
                  <span className="font-medium">Explanation:</span> {question.explanation}
                </p>
              )}
            </div>
          </div>
        ))}
        
        <div className="flex justify-end mt-4">
          {showResults ? (
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          ) : (
            <Button onClick={handleCheckAnswers}>Check Answers</Button>
          )}
        </div>
      </div>
    </div>
  );
}
