import { useQuery } from "@tanstack/react-query";
import { SpeakingTopic } from "@shared/schema";
import { SkillCard } from "@/components/skills/SkillCard";
import { SpeakingTopicCard } from "@/components/speaking/SpeakingTopicCard";
import { AvatarChat } from "@/components/speaking/AvatarChat";
import { useAuth } from "@/hooks/use-auth";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "wouter";

export default function Speaking() {
  const { user } = useAuth();
  const [location] = useLocation();
  const match = location.match(/\/speaking\/(\d+)/);
  const topicId = match ? parseInt(match[1]) : null;

  const { data: topics, isLoading: topicsLoading } = useQuery<SpeakingTopic[]>({
    queryKey: ["/api/speaking-topics"],
  });

  const { data: topic, isLoading: topicLoading } = useQuery<SpeakingTopic>({
    queryKey: [`/api/speaking-topics/${topicId}`],
    enabled: !!topicId,
  });

  const { data: skills } = useQuery({
    queryKey: [`/api/skills/${user?.id}`],
    enabled: !!user,
  });

  const speakingSkill = skills?.find(skill => skill.type === "speaking");
  
  const proficiency = speakingSkill?.proficiency || 0;
  const lastActivity = speakingSkill ? new Date(speakingSkill.lastActivity) : new Date();
  
  const getLastActivityText = (date: Date) => {
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    return `${diffDays} days ago`;
  };

  const isLoading = topicsLoading || (topicId && topicLoading);

  return (
    <SkillCard
      title="Speaking Practice"
      description="Practice your German speaking skills with our AI conversation partner"
      icon={<span className="material-icons mr-2 text-primary">record_voice_over</span>}
      proficiency={proficiency}
      lastActivity={getLastActivityText(lastActivity)}
    >
      {topicId && topic ? (
        <AvatarChat topic={topic} />
      ) : (
        <>
          <h3 className="font-bold text-neutral-darker text-lg mb-4">Practice Topics</h3>
          
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-32 w-full" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {topics?.map(topic => (
                <SpeakingTopicCard key={topic.id} topic={topic} />
              ))}
            </div>
          )}
        </>
      )}
    </SkillCard>
  );
}
