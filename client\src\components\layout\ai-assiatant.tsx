import React, { useState, useTransition } from 'react'
import { FaMicrophone, FaMicrophoneSlash } from 'react-icons/fa'
import { RxCross2 } from 'react-icons/rx'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip'
import { Button } from '../ui/button'
import { LiveAudioVisualizer } from 'react-audio-visualize';
import { axiosInstance } from '@/lib/queryClient'
import { toast } from '@/hooks/use-toast'

const AIAssistantDialog = ({ setOpen, open, isRecording, setIsRecording }: any) => {
    const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder>();
    const [audioChunks, setAudioChunks] = useState<BlobPart[]>([]);
    const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
    const [audioMimeType, setAudioMimeType] = useState<string>('audio/wav');
    const [isPending, startTransition] = useTransition();

    return (
        <Dialog open={open} onOpenChange={(open) => {
            if (!open) {
                return;
            }
            setOpen(open);
        }} >
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <DialogTrigger asChild>
                            <Button
                                size="lg"
                                className="fixed top-[75px] right-5 h-14 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all  group z-50"
                            >
                                <p className="text-2xl">🪄</p> Ask AI
                            </Button>
                        </DialogTrigger>
                    </TooltipTrigger>

                    <TooltipContent side="left" >
                        <p>AI Assistant</p>
                    </TooltipContent>

                </Tooltip>
            </TooltipProvider>

            <DialogContent className="sm:max-w-md bg-white !rounded-[.7rem] [&>button]:hidden">
                <DialogHeader>
                    <DialogTitle>AI Assistant</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 py-2">
                    <p>How can I help you with this exercise?</p>
                    <div className="flex flex-col gap-4">
                        <div className="relative w-full h-[300px] bg-gray-100 rounded-xl">
                            {/* Video preview will be shown here */}
                            <video
                                className="w-full h-full object-cover rounded-xl"
                                id="video-preview"
                            />
                        </div>

                        <div className="flex items-center justify-center gap-6">
                            <button
                                className="p-3 rounded-full text-white transition-colors border-2"
                                aria-label="Toggle microphone"
                                onClick={async () => {
                                    // Send audio to server here
                                    if (isRecording) {
                                        mediaRecorder?.stop();
                                        mediaRecorder?.addEventListener('stop', () => {
                                            const audioBlob = new Blob(audioChunks, { type: audioMimeType }); 
                                            setAudioBlob(audioBlob);
                                            console.log('Recorded audio blob:', audioBlob);
                                            startTransition(() => {
                                                const sendAudio = async () => {
                                                    try {
                                                        await axiosInstance.post('/ai/transcribe', {
                                                            audio: audioBlob,
                                                        });
                                                    } catch (error) {
                                                        toast({
                                                            title: 'Error',
                                                            description: 'Something went wrong',
                                                            variant: 'destructive',
                                                        })
                                                    }
                                                };
                                                sendAudio();
                                            });
                                        });
                                        setMediaRecorder(undefined)
                                        setIsRecording(false)
                                        setAudioChunks([])
                                    }
                                    // Record audio here
                                    else {
                                        navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
                                            // Set options for WAV format
                                            const options = { mimeType: 'audio/wav' };
                                            
                                            // Try to create recorder with WAV, fall back to default if not supported
                                            let recorder;
                                            try {
                                                recorder = new MediaRecorder(stream, options);
                                                setAudioMimeType('audio/wav');
                                            } catch (e) {
                                                console.log('WAV not supported, using default format');
                                                recorder = new MediaRecorder(stream);
                                                // Get the actual MIME type from the recorder
                                                const actualMimeType = recorder.mimeType;
                                                console.log('Using format:', actualMimeType);
                                                setAudioMimeType(actualMimeType || 'audio/webm');
                                            }

                                            // Add event listener to capture audio chunks
                                            recorder.ondataavailable = (event) => {
                                                setAudioChunks(chunks => [...chunks, event.data]);
                                            };

                                            setMediaRecorder(recorder);
                                            // Start recording and request data every 100ms
                                            recorder.start(100);
                                        });
                                        // setIsRecording(true);
                                    }
                                    setIsRecording(!isRecording)
                                }}

                            >
                                {isRecording ? (
                                    <FaMicrophone
                                        size={28}
                                        color="red"
                                    />
                                ) : (
                                    <FaMicrophoneSlash
                                        size={28}
                                        color="gray"
                                    />
                                )}
                            </button>

                            <RxCross2
                                size={55}
                                onClick={() => {
                                    setOpen(false)
                                    setIsRecording(false)
                                    mediaRecorder?.stop()
                                    setMediaRecorder(undefined)
                                }}
                                color="gray"
                                className="bg-gray-100 text-black border-2 p-[14px] font-bold rounded-full cursor-pointer"
                            />
                        </div>
                    </div>
                </div>
                {mediaRecorder && (
                    <div className='w-full overflow-hidden'>
                        <LiveAudioVisualizer
                            mediaRecorder={mediaRecorder}
                            width={600}
                            height={60}
                            barWidth={2}
                            gap={1}
                            fftSize={512}
                        />
                    </div>
                )}
            </DialogContent>
        </Dialog >
    )
}

export default AIAssistantDialog
